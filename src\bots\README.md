# Minesweeper Bots

This directory contains implementations of various bots for playing Minesweeper.

## Bot Implementations

- `bayes_bot.py`: A Bayesian probability-based bot that calculates the likelihood of mines in each cell
- `simple_logic_bot.py`: A simple logic-based bot that uses deterministic rules to identify safe cells and mines
- `nn_bot.py`: A neural network-based bot that uses a trained model to predict safe moves

## Usage

Each bot has a similar interface:

1. Create a bot instance
2. Call `make_move(game)` to get the next move
3. Update the bot's state with `update_from_game(game)` after each move

### Example: Using the SimpleLogicBot

```python
from src.bots.simple_logic_bot import SimpleLogicBot
from src.game.MineSweeper import MineSweeper

# Create a game
game = MineSweeper(H=9, W=9, M=10)
game.start()  # Make the first move

# Create a bot
bot = SimpleLogicBot(9, 9, 10)

# Game loop
while not game.game_over:
    # Get the next move from the bot
    move = bot.make_move(game)
    
    if move is None:
        print("<PERSON><PERSON> could not find a move")
        break
    
    # Make the move
    result = game.make_move(move[0], move[1])
    
    # Check if the game is over
    if result["mine_triggered"]:
        print("Mine triggered!")
        break
    
    if game.win_condition_met:
        print("Game won!")
        break
```

### Example: Using the Neural Network Bot

```python
from src.bots.nn_bot import nn_Bot
from src.game.MineSweeper import MineSweeper

# Create a bot with a saved model
bot = nn_Bot(model_path="models/task1/minesweeper_model_best.h5")

# Create a game
game = MineSweeper(H=9, W=9, M=10)

# Play the game
result = bot.play_game(game, verbose=True)
```
