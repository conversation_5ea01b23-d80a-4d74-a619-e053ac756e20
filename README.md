# 🎯 Minesweeper Deep Learning Final Project (CS462)

A neural network implementation for playing Minesweeper, addressing three main tasks: Traditional Boards, Variable Mine Densities, and Variable Board Sizes. This project compares neural network performance against rule-based logic bots across different game configurations.

## 📁 Project Structure

```
Minesweeper_DL-Final_Project_CS462--1/
├── src/
│   ├── bots/                    # Bot implementations
│   │   ├── __init__.py
│   │   ├── BayesBot.py         # Bayesian inference bot
│   │   ├── nn_bot.py           # Neural network bot
│   │   └── SimpleLogicBot.py   # Rule-based logic bot
│   ├── game/                    # Core game engine
│   │   ├── __init__.py
│   │   ├── Inference.py        # Game inference utilities
│   │   └── MineSweeper.py      # Main game implementation
│   ├── models/                  # Training scripts
│   │   ├── __init__.py
│   │   ├── model_utils.py      # Shared model utilities
│   │   ├── TM_easy.py          # Task 1: Easy (9x9, 10 mines)
│   │   ├── TM_expert.py        # Task 1: Expert (30x16, 99 mines)
│   │   ├── TM_intermediate.py  # Task 1: Intermediate (16x16, 40 mines)
│   │   ├── TM_variable_mines.py # Task 2: Variable mine densities
│   │   ├── TM_variable_size.py  # Task 3: Variable board sizes
│   │   └── train_launcher.py   # Unified training launcher
│   ├── main.py                 # Interactive play and simulation
│   ├── simulations.py          # Data generation
│   └── evaluate_bots.py        # Bot performance comparison
├── .gitignore
├── Minesweeper_Final_Project.txt # Official project requirements
├── README.md                   # This file
└── requirements.txt            # Python dependencies
```

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Verify TensorFlow installation
python -c "import tensorflow as tf; print('TensorFlow version:', tf.__version__)"
```

### 2. Data Generation
```bash
# Generate training data for all tasks
python src/simulations.py --production-mode

# Generate smaller dataset for testing
python src/simulations.py --test-mode
```

### 3. Model Training
```bash
# Train models for specific difficulties
python src/models/TM_easy.py          # Easy: 9x9, 10 mines
python src/models/TM_intermediate.py  # Intermediate: 16x16, 40 mines
python src/models/TM_expert.py        # Expert: 30x16, 99 mines

# Train models for variable configurations
python src/models/TM_variable_mines.py # Variable mine densities
python src/models/TM_variable_size.py  # Variable board sizes

# Use unified launcher for batch training
python src/models/train_launcher.py --model all
```

### 4. Bot Evaluation
```bash
# Compare bots on traditional difficulties
python src/evaluate_bots.py --model path/to/easy_model.h5 --difficulty easy --games 1000
python src/evaluate_bots.py --model path/to/intermediate_model.h5 --difficulty intermediate --games 1000
python src/evaluate_bots.py --model path/to/expert_model.h5 --difficulty expert --games 1000

# Evaluate on custom configurations
python src/evaluate_bots.py --model path/to/model.h5 --custom --board-size 20 20 --mine-density 0.15 --games 500
python src/evaluate_bots.py --model path/to/model.h5 --custom --board-size 15 15 --mines 30 --games 500
```

## 🎮 Project Tasks

### Task 1: Traditional Minesweeper Boards
Train neural networks to play standard Minesweeper configurations:
- **Easy**: 9×9 board with 10 mines
- **Intermediate**: 16×16 board with 40 mines
- **Expert**: 30×16 board with 99 mines

**Evaluation Metrics**:
- Win rate comparison (Logic Bot vs Neural Bot)
- Average steps survived before hitting first mine
- Average mines triggered when continuing after mine hits

### Task 2: Variable Numbers of Mines
Build a network capable of handling variable mine densities:
- **Board Size**: 30×30 fixed
- **Mine Density**: 0% to 30% of board (0-270 mines)
- **Focus**: Adaptability to different mine concentrations

### Task 3: Variable Size Boards
Create a single network architecture for variable board sizes:
- **Size Range**: K×K boards where K > 5
- **Target Range**: 5×5 to 50×50 boards
- **Mine Density**: ~20% or configurable
- **Challenge**: Single model for all sizes (no hardcoded limits)

## 🤖 Bot Implementations

### SimpleLogicBot
Rule-based bot implementing the logic described in project specifications:
- Deterministic inference rules for identifying safe cells and mines
- Random selection when no logical moves available
- Baseline for neural network comparison

### Neural Network Bot
Deep learning model with:
- Convolutional neural network architecture
- Game state representation as multi-channel input
- Probability-based move selection
- Trained on simulated game data

### BayesBot (Additional)
Advanced probabilistic bot using Bayesian inference for enhanced comparison.

## 📊 Evaluation and Analysis

The `evaluate_bots.py` script provides comprehensive performance analysis:

**Key Metrics**:
- **Win Rate**: Percentage of games completed successfully
- **Average Steps**: Mean number of moves before game end
- **Mine Triggers**: Average mines hit during gameplay

**Output Features**:
- Statistical comparison tables
- Performance difference calculations
- Visualization plots (win rate, steps, mines triggered)
- Detailed JSON results for further analysis

**Example Results Format**:
```
EVALUATION RESULTS: EASY
============================================================

Logic Bot Performance:
  Win Rate: 0.8450 (84.50%)
  Average Steps: 45.23
  Average Mines Triggered: 0.18

Neural Bot Performance:
  Win Rate: 0.8720 (87.20%)
  Average Steps: 48.67
  Average Mines Triggered: 0.15

Performance Differences (Neural - Logic):
  Win Rate Difference: +0.0270
  Steps Difference: +3.44
  Mines Difference: -0.03

✅ Neural bot has 2.70% higher win rate
```

## 🎯 Interactive Play

Test bots interactively or run single-game simulations:

```bash
# Interactive game with different bots
python src/main.py

# The script provides options to:
# 1. Simulate games with BayesBot
# 2. Simulate games with SimpleLogicBot
# 3. Play random games (baseline)
# 4. Exit
```

## 📋 Workflow Summary

**Complete Project Workflow**:

1. **Setup Environment**
   ```bash
   pip install -r requirements.txt
   ```

2. **Generate Training Data**
   ```bash
   python src/simulations.py --production-mode
   ```

3. **Train Models**
   ```bash
   # Train all models
   python src/models/train_launcher.py --model all

   # Or train individually
   python src/models/TM_easy.py
   python src/models/TM_intermediate.py
   python src/models/TM_expert.py
   python src/models/TM_variable_mines.py
   python src/models/TM_variable_size.py
   ```

4. **Evaluate Performance**
   ```bash
   # Standard evaluations
   python src/evaluate_bots.py --model models/easy_model.h5 --difficulty easy --games 1000
   python src/evaluate_bots.py --model models/intermediate_model.h5 --difficulty intermediate --games 1000
   python src/evaluate_bots.py --model models/expert_model.h5 --difficulty expert --games 1000

   # Custom evaluations
   python src/evaluate_bots.py --model models/variable_model.h5 --custom --board-size 25 25 --mine-density 0.2 --games 500
   ```

5. **Analyze Results**
   - Review console output for performance summaries
   - Check generated plots (`bot_comparison.png`)
   - Examine detailed JSON results files

## 🔧 Technical Details

**Neural Network Architecture**:
- Convolutional layers for spatial pattern recognition
- Multi-channel input representation (revealed cells, clues, flags, etc.)
- Output layer predicting move probabilities for each cell
- Trained using supervised learning on simulated game data

**Data Generation**:
- Automated game simulation with various bot strategies
- State-action pairs collected during gameplay
- Balanced datasets across different game configurations
- Preprocessing for neural network input format

**Model Training**:
- TensorFlow/Keras implementation
- Configurable hyperparameters
- Validation split for overfitting prevention
- Model checkpointing and performance monitoring

## 📝 Project Requirements

This implementation addresses all requirements from `Minesweeper_Final_Project.txt`:

- ✅ **Task 1**: Neural networks for traditional board sizes with performance comparison
- ✅ **Task 2**: Variable mine density handling (0-30% on 30x30 boards)
- ✅ **Task 3**: Single architecture for variable board sizes (K×K, K>5)
- ✅ **Evaluation**: Comprehensive bot comparison with statistical analysis
- ✅ **Documentation**: Clear workflow and usage instructions

## 🚨 Important Notes

- Ensure sufficient disk space for training data generation
- Model training can be computationally intensive; consider using GPU acceleration
- Evaluation with 1000+ games provides more reliable statistical comparisons
- Results may vary between runs due to randomness in game generation and neural network training

## 📄 License

This project is developed for academic purposes as part of CS462 coursework.
