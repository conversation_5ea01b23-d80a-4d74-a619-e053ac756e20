import numpy as np
import tensorflow as tf
import os
import time # Added for example usage delay
import traceback # Added for detailed error printing
from tqdm import tqdm # Import tqdm for progress bar

# Assuming MineSweeper is in the correct path relative to this script
# Adjust the import path if necessary
try:
    # If running from project root where 'src' is a subdir containing 'game'
    from src.game.MineSweeper import MineSweeper
except ImportError:
    # Fallback if structure is different (e.g., script inside src/models)
    import sys
    try:
        # This assumes the script is run from within a subdirectory like 'src/models'
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
        if project_root not in sys.path:
             sys.path.insert(0, project_root) # Add project root to path if not already there
        # Now try importing from the 'src' perspective
        from src.game.MineSweeper import MineSweeper
        print(f"Successfully imported MineSweeper using project root: {project_root}")
    except ImportError as e:
        print(f"Failed to import MineSweeper. Check PYTHONPATH and file location relative to project root. Error: {e}")
        # Consider adding more fallback paths if needed
        print("Could not find MineSweeper game class.")
        sys.exit(1) # Exit if game class cannot be found


# --- Custom Objects Definition ---
# Define custom objects used during training (needed for loading)
# **IMPORTANT**: These definitions MUST EXACTLY match those used during training/saving.

@tf.keras.utils.register_keras_serializable()
def move_accuracy(y_true, y_pred):
    """Accuracy: % of times the highest probability prediction matches the target move."""
    y_true_idx = tf.argmax(y_true, axis=1)
    y_pred_idx = tf.argmax(y_pred, axis=1)
    correct = tf.equal(y_true_idx, y_pred_idx)
    return tf.reduce_mean(tf.cast(correct, tf.float32))

# Factory function for top-k accuracy metrics
def top_k_move_accuracy(k=3):
    """Top-K Accuracy: % of times the target move is within the top K predictions."""
    # *** FIX: Added the necessary decorator to the inner function for loading ***
    # This decorator MUST match the one used in the training script.
    @tf.keras.utils.register_keras_serializable(package='Custom', name=f'top_{k}_accuracy')
    def top_k_acc(y_true, y_pred):
        y_true_idx = tf.argmax(y_true, axis=1, output_type=tf.int32)
        y_true_idx = tf.expand_dims(y_true_idx, axis=1) # Shape: (batch, 1)
        _, top_k_indices = tf.nn.top_k(y_pred, k=k, sorted=True) # Shape: (batch, k)
        top_k_indices = tf.cast(top_k_indices, tf.int32)
        # Check if the true index is present anywhere in the top K predicted indices
        correct = tf.reduce_any(tf.equal(y_true_idx, top_k_indices), axis=1) # Shape: (batch,)
        return tf.reduce_mean(tf.cast(correct, tf.float32))

    # Assign specific names that match how they were likely compiled/saved
    # These names MUST match the keys used in the custom_objects dictionary below
    # and the metric names used during model.compile() in the training script.
    if k == 3: top_k_acc.__name__ = 'top_3_accuracy'
    elif k == 5: top_k_acc.__name__ = 'top_5_accuracy'
    else: top_k_acc.__name__ = f'top_{k}_accuracy' # Generic name for other k values
    return top_k_acc

@tf.keras.utils.register_keras_serializable()
def weighted_categorical_crossentropy_loss(y_true, y_pred, label_smoothing=0.0):
    """
    Categorical crossentropy loss function.
    Needed for loading models trained with this as a custom loss.
    Ensure 'label_smoothing' default matches the value used during training if applicable.
    """
    # Apply label smoothing if used during training
    if label_smoothing > 0:
        n_classes = tf.cast(tf.shape(y_true)[-1], y_true.dtype)
        y_true = y_true * (1.0 - label_smoothing) + (label_smoothing / n_classes)

    # Clip predictions for numerical stability before passing to loss function
    y_pred = tf.clip_by_value(y_pred, tf.keras.backend.epsilon(), 1. - tf.keras.backend.epsilon())

    # Use Keras's built-in loss calculation.
    # Assumes y_pred comes from a Softmax layer (probabilities), hence from_logits=False.
    return tf.keras.losses.categorical_crossentropy(y_true, y_pred, from_logits=False)


# Create dictionary of custom objects for loading
# Ensure the keys here EXACTLY match the names used when compiling the model
# and the __name__ assigned within the factory function above.
custom_objects = {
    'move_accuracy': move_accuracy,
    'top_3_accuracy': top_k_move_accuracy(k=3), # Key matches __name__ for k=3
    'top_5_accuracy': top_k_move_accuracy(k=5), # Key matches __name__ for k=5
    'weighted_categorical_crossentropy_loss': weighted_categorical_crossentropy_loss
}


class nn_Bot:
    """
    A bot that uses a trained Keras model to play Minesweeper.
    """
    def __init__(self, model_path=None, model=None):
        """
        Initialize the neural network bot with either a path to a saved model
        or a pre-loaded model instance.

        Args:
            model_path (str, optional): Path to the saved TensorFlow model (.keras format recommended).
            model (tf.keras.Model, optional): A pre-loaded TensorFlow model instance.
        """
        self.model = None
        self.H = -1 # Model's expected input height
        self.W = -1 # Model's expected input width
        self.C = -1 # Model's expected input channels

        if model is not None:
            if isinstance(model, tf.keras.Model):
                self.model = model
                print("Using pre-loaded model.")
            else:
                 raise TypeError("Provided 'model' must be a tf.keras.Model instance.")
        elif model_path is not None:
            self.load_model(model_path)
        else:
            raise ValueError("Either 'model_path' or a pre-loaded 'model' instance must be provided.")

        if self.model is None:
             # This should ideally not be reached if load_model raises on failure
             raise ValueError("Model could not be initialized.")

        # Store input shape details from the loaded model
        self._get_model_input_shape()

    def _get_model_input_shape(self):
        """Helper method to extract H, W, C from the model's input shape."""
        try:
            # Input shape is typically (None, H, W, C) where None is batch size
            # Accessing internal _functional_input_shape might be more reliable for some models
            if hasattr(self.model, '_functional_input_shape'):
                 input_shape = self.model._functional_input_shape
            else:
                 input_shape = self.model.input_shape

            if isinstance(input_shape, (list, tuple)) and len(input_shape) == 4:
                # Check if dimensions are defined
                if all(isinstance(dim, int) for dim in input_shape[1:]):
                    self.H = input_shape[1]
                    self.W = input_shape[2]
                    self.C = input_shape[3]
                    print(f"Model expects input shape: H={self.H}, W={self.W}, C={self.C}")
                else:
                    print(f"Warning: Model input shape has undefined dimensions {input_shape}. Attempting fallback.")
                    # Try to get concrete shape (might fail)
                    try:
                        concrete_func = self.model.signatures["serving_default"]
                        concrete_input_shape = concrete_func.inputs[0].shape.as_list()
                        if len(concrete_input_shape) == 4 and all(isinstance(dim, int) for dim in concrete_input_shape[1:]):
                             self.H, self.W, self.C = concrete_input_shape[1], concrete_input_shape[2], concrete_input_shape[3]
                             print(f"Inferred concrete input shape: H={self.H}, W={self.W}, C={self.C}")
                        else:
                             raise ValueError("Could not infer concrete shape.")
                    except Exception as fallback_e:
                        print(f"Could not get concrete input shape via serving_default: {fallback_e}. H, W, C remain undefined (-1).")
                        self.H, self.W, self.C = -1, -1, -1
            else:
                print(f"Warning: Unexpected model input shape format: {input_shape}. Cannot determine H, W, C.")
                self.H, self.W, self.C = -1, -1, -1

        except Exception as e:
            print(f"Warning: Could not automatically determine input shape from model: {e}")
            self.H, self.W, self.C = -1, -1, -1 # Indicate unknown shape

    def load_model(self, model_path):
        """Load a saved TensorFlow model from the specified path."""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model path not found: {model_path}")

        print(f"Loading model from {model_path}...")
        try:
            # Provide custom objects dictionary defined globally
            self.model = tf.keras.models.load_model(model_path, custom_objects=custom_objects)
            print("Model loaded successfully.")
        except Exception as e:
            # Provide more context on potential custom object errors
            print(f"ERROR: Failed to load model from {model_path}.")
            print(f"  Reason: {str(e)}")
            print("  Hint: If the error mentions an unknown function/metric/loss (like 'top_3_accuracy'),")
            print("  ensure it's defined globally (like above this class) AND decorated with")
            print("  `@tf.keras.utils.register_keras_serializable(...)` matching the training script.")
            print("  Also ensure the function name and the key in the `custom_objects` dictionary")
            print("  EXACTLY match the one used during model compilation/saving.")
            traceback.print_exc() # Print full traceback for debugging
            raise # Re-raise the exception to prevent using an uninitialized model

    def preprocess_game_state(self, nn_input_state):
        """
        Preprocess the game state into the format expected by the trained model.
        This MUST match the preprocessing applied during training.

        Args:
            nn_input_state (np.ndarray): The output of MineSweeper.get_board_state_for_nn()
                                        Expected shape: (H, W, C) numpy array.

        Returns:
            np.ndarray: A numpy array ready for model prediction, shape (1, H, W, C).
        """
        # Ensure input is a numpy array of the correct type
        input_data = np.array(nn_input_state, dtype=np.float32)

        # --- Shape Verification ---
        if input_data.ndim != 3:
            raise ValueError(f"Input state must be 3D (H, W, C), got {input_data.ndim}D shape {input_data.shape}")

        # CRITICAL FIX: Handle variable-size models that require padding
        if self.H > 0 and self.W > 0 and self.C > 0: # Check if model shape is known
            current_h, current_w, current_c = input_data.shape

            # Check if this is a variable-size model (typically 50x50 for this project)
            is_variable_size_model = (self.H >= 50 and self.W >= 50)

            if input_data.shape != (self.H, self.W, self.C):
                if is_variable_size_model and current_h <= self.H and current_w <= self.W and current_c == self.C:
                    # Pad smaller board to model's expected size
                    print(f"Padding board from {input_data.shape} to ({self.H}, {self.W}, {self.C}) for variable-size model")
                    padded_data = np.zeros((self.H, self.W, self.C), dtype=input_data.dtype)
                    padded_data[:current_h, :current_w, :current_c] = input_data

                    # Update mask channel (channel 10) to indicate valid board area
                    if self.C > 10:
                        # Set mask to 1.0 for valid board area, 0.0 for padded area
                        padded_data[:, :, 10] = 0.0  # Clear entire mask
                        padded_data[:current_h, :current_w, 10] = 1.0  # Set valid area

                    input_data = padded_data
                    print(f"Successfully padded board to {input_data.shape}")
                else:
                    raise ValueError(f"Input state shape mismatch. Model expects ({self.H},{self.W},{self.C}), got {input_data.shape}")
        else:
             # If model shape isn't known, we can't strictly verify, but log a warning maybe?
             # print("Warning: Model input shape unknown, cannot verify input state dimensions.")
             pass # Proceed with caution

        current_C = input_data.shape[-1] # Actual channels in the provided state

        # --- Apply the same preprocessing as in the training script's `preprocess_data` function ---
        # Normalize mine density channel (assumed to be channel index 11, i.e., the 12th channel)
        # Check if this channel actually exists in the input
        if current_C > 11:
            mine_density_channel = input_data[..., 11:12] # Slice the 12th channel (index 11)
            # Ensure clipping and division matches training exactly
            normalized_density = np.clip(mine_density_channel, 0.0, 0.5) / 0.5
            # Get channels before and after the density channel
            other_channels_before = input_data[..., :11]
            # Concatenate back together
            if current_C > 12: # If there are channels *after* density
                channels_after_density = input_data[..., 12:]
                input_data = np.concatenate([other_channels_before, normalized_density, channels_after_density], axis=-1)
            else: # If density was the last channel
                input_data = np.concatenate([other_channels_before, normalized_density], axis=-1)
        # --- End of Preprocessing ---

        # Add batch dimension for prediction
        input_data = np.expand_dims(input_data, axis=0) # Shape: (1, H, W, C)
        return input_data

    def choose_next_move(self, nn_input_state):
        """
        Use the model to choose the next move based on the current game state.
        Selects the unrevealed cell with the highest predicted probability.

        Args:
            nn_input_state (np.ndarray): The current state from game.get_board_state_for_nn() (H, W, C).

        Returns:
            tuple: (row, col) representing the next move. Returns None if prediction fails or no valid move found.
        """
        # Infer H, W from the state itself for robustness in case model shape wasn't loaded
        if not isinstance(nn_input_state, np.ndarray) or nn_input_state.ndim != 3:
            print(f"Error: choose_next_move expects a 3D numpy array state (H,W,C), got {type(nn_input_state)} with shape {getattr(nn_input_state, 'shape', 'N/A')}")
            return None
        H_current, W_current, C_current = nn_input_state.shape

        # Preprocess the game state (applying normalization etc.)
        try:
            processed_state = self.preprocess_game_state(nn_input_state)
        except ValueError as e:
            print(f"Error preprocessing state: {e}")
            return None # Cannot proceed

        # Make prediction with the model
        try:
            # model.predict is generally fine for single inferences.
            # Adding verbose=0 suppresses the progress bar for single predictions.
            predictions_batch = self.model.predict(processed_state, verbose=0)
            # Result shape is (1, H*W), extract the first element
            predictions_flat = predictions_batch[0] # Shape (H*W,)

            if predictions_flat.shape != (H_current * W_current,):
                 print(f"Warning: Prediction shape mismatch. Expected ({H_current * W_current},), got {predictions_flat.shape}")
                 # Attempt to reshape if possible, otherwise error
                 if np.prod(predictions_flat.shape) == H_current * W_current:
                      predictions_flat = predictions_flat.reshape(H_current * W_current)
                 else:
                      print("Cannot reshape prediction to match board size. Aborting move choice.")
                      return None


        except Exception as e:
            print(f"ERROR during model prediction: {str(e)}")
            traceback.print_exc() # Print full traceback
            return None # Indicate failure to choose a move

        # --- Post-process Predictions: Mask Revealed Cells ---
        # Get revealed cells mask (Channel 0: is_revealed) directly from the *original* input state
        # Ensure it's flattened and boolean type
        try:
            revealed_mask_flat = nn_input_state[:, :, 0].flatten().astype(bool)
            if revealed_mask_flat.shape != (H_current * W_current,):
                 print(f"Error: Revealed mask shape mismatch. Expected ({H_current * W_current},), got {revealed_mask_flat.shape}")
                 return None
        except IndexError:
             print(f"Error: Could not access channel 0 (revealed mask) in input state with shape {nn_input_state.shape}")
             return None

        # Create a copy of predictions to modify without altering original probabilities
        masked_predictions = np.copy(predictions_flat)

        # Set probability of already revealed cells to a very low value (-infinity works well with argmax)
        # This prevents the bot from choosing an already revealed cell
        masked_predictions[revealed_mask_flat] = -np.inf

        # Find the index of the best *valid* (unrevealed) move according to the model
        best_move_flat_idx = np.argmax(masked_predictions)

        # --- Sanity Check and Fallback ---
        # Check if the highest probability move is still invalid (e.g., all cells revealed/masked)
        # This might happen if the game is won but the loop continues, or if there's an issue.
        if masked_predictions[best_move_flat_idx] == -np.inf:
            print("Warning: NN Bot could not find a valid move (all probabilities masked). Falling back.")
            # Fallback Strategy: Find the first unrevealed cell available (simple deterministic fallback)
            unrevealed_indices = np.where(~revealed_mask_flat)[0]
            if len(unrevealed_indices) > 0:
                best_move_flat_idx = unrevealed_indices[0] # Choose the first one
                print(f"Fallback: Choosing first unrevealed cell at flat index {best_move_flat_idx}")
            else:
                # This state should ideally not be reached if game logic terminates correctly
                print("Critical Error: No unrevealed cells left, but game is not over? Returning (0,0).")
                # Returning (0,0) might still cause an error if that cell is revealed.
                # Returning None might be safer to indicate failure.
                return None # Indicate failure to find any move

        # Convert the flat index back to (row, col) using the current game's 2D shape
        move_row, move_col = np.unravel_index(best_move_flat_idx, (H_current, W_current))

        return int(move_row), int(move_col) # Ensure integer coordinates

    def play_game(self, game: MineSweeper, verbose=True):
        """
        Play a Minesweeper game using the bot and return the result.

        Args:
            game (MineSweeper): A MineSweeper game instance.
            verbose (bool): Whether to print game progress and board states.

        Returns:
            str: Game result ("win", "loss", "error", "bot_error").
        """
        moves_made = 0
        if verbose:
            print(f"\n--- Starting New Game ({game.H}x{game.W}, {game.M} mines) ---")
            # Ensure board is initialized before printing (start() handles this)
            if not game.is_started: game.start() # Start if not already started
            print("Initial Board:")
            print(game) # Print initial board

        while not game.game_over:
            # 1. Get the current state for the NN
            try:
                # Get the state *before* making the move
                nn_input_state = game.get_board_state_for_nn()
            except Exception as e:
                print(f"Error getting board state for NN: {e}")
                traceback.print_exc()
                return "error" # Indicate a game-related error

            # 2. Choose next move using the NN
            move_coords = self.choose_next_move(nn_input_state)

            if move_coords is None:
                print("Bot failed to choose a move. Ending game.")
                # This indicates an issue within the bot/model itself
                return "bot_error" # Indicate bot failure

            move_row, move_col = move_coords
            if verbose:
                # Optional: Add predicted probability? Requires choose_next_move to return it.
                print(f"\nMove {moves_made + 1}: Bot selects ({move_row}, {move_col})")

            # 3. Make the move in the game
            try:
                result = game.make_move(move_row, move_col)
                moves_made += 1

                if verbose:
                    print("Board after move:")
                    print(game) # Print board after move

                # Check game end conditions from the result dictionary returned by make_move
                if result.get("mine_triggered", False):
                    if verbose:
                        print(f"**** MINE HIT at ({move_row}, {move_col})! Game Over. ****")
                    # game.game_over should be true now, loop will terminate naturally
                elif result.get("won", False):
                     if verbose:
                         print(f"**** GAME WON! ****")
                     # game.game_over should be true now, loop will terminate naturally

            except IndexError:
                print(f"Error: Move ({move_row}, {move_col}) is out of bounds for board ({game.H}x{game.W}). Bot generated invalid move?")
                traceback.print_exc()
                return "error" # Indicate an error (potentially bot's fault)
            except Exception as e:
                print(f"Error making move ({move_row}, {move_col}) in game engine: {str(e)}")
                traceback.print_exc()
                return "error" # Indicate a game-related error

            # Optional: Add delay for visualization/debugging
            # if verbose: time.sleep(0.1)

        # --- Game End ---
        # Use the game's final result method after the loop finishes
        final_result = game.get_result() # Should be 'win' or 'loss'
        if verbose:
            print("-" * 20)
            print(f"Game finished after {moves_made} moves.")
            print(f"Final Result: {final_result.upper()}")
            print("-" * 20)

        return final_result


# Example usage:
if __name__ == "__main__":

    # --- Configuration ---
    # !! IMPORTANT: REPLACE with the actual path to YOUR trained model !!
    # Ensure the path uses correct separators for your OS (e.g., / for Linux/WSL, \ for Windows)
    # Example filenames (replace with your actual model file):
    # MODEL_FILENAME = "simple_cnn_H9W9_M9k_YYYYMMDD_HHMMSS.keras"
    MODEL_FILENAME = "simple_cnn_H9W9_20250413_040011.keras" # 57% win rate
    # MODEL_FILENAME = "simple_cnn_H9W9_20250412_105348.keras"
    # MODEL_FILENAME = "simple_cnn_H9W9_20250412_035226.keras"
    # MODEL_FILENAME = "simple_cnn_H9W9_20250412_034955.keras"
    # MODEL_FILENAME = "simple_cnn_H9W9_20250412_034724.keras"
    # MODEL_FILENAME = "simple_cnn_H9W9_20250412_034625.keras"
    # MODEL_FILENAME = "simple_cnn_H9W9_20250411_160708.keras" #64% win rate
    # MODEL_FILENAME = "simple_cnn_H9W9_20250409_151616.keras" #63% win rate


    NUM_GAMES_TO_TEST = 100 # Set how many games to play for testing
    VERBOSE_PLAY = False    # Set to True to see output for each game, False for just summary

    # Game parameters for testing
    # These SHOULD ideally match the dimensions the model was trained on for best results.
    # The script will warn if there's a mismatch detected after loading the model.
    GAME_HEIGHT = 9
    GAME_WIDTH = 9
    NUM_MINES = 10

    # --- Construct Model Path ---
    model_path_constructed = None
    try:
        # Assume script is in a subdirectory like 'src/bots'
        script_dir = os.path.dirname(__file__)
        # Navigate up two levels to the project root
        project_root_dir = os.path.abspath(os.path.join(script_dir, '..', '..'))
        # Construct path to the models directory
        model_dir = os.path.join(project_root_dir, "models", "trained_simple")
        # Construct the full path to the model file
        model_path_constructed = os.path.join(model_dir, MODEL_FILENAME)
        print(f"Project root detected: {project_root_dir}")
        print(f"Model directory targeted: {model_dir}")

    except NameError:
        # Fallback if __file__ is not defined (e.g., running in an interactive notebook)
        print("Warning: Could not determine script directory via __file__. Using relative path for model.")
        # Adjust relative path based on where you run the script from
        # Example: If running from project root, path might be:
        model_dir = os.path.join("models", "trained_simple")
        # Example: If running from 'src', path might be:
        # model_dir = os.path.join("..", "models", "trained_simple")
        model_path_constructed = os.path.join(model_dir, MODEL_FILENAME)


    print(f"Attempting to load model from: {model_path_constructed}")

    # --- Initialization & Gameplay Loop ---
    if not os.path.exists(model_path_constructed):
         print(f"\n--- FATAL ERROR ---")
         print(f"Model file not found at the constructed path: {model_path_constructed}")
         print("Please verify:")
         print(f"  1. The MODEL_FILENAME ('{MODEL_FILENAME}') is correct.")
         print(f"  2. The model file exists in the directory: '{os.path.dirname(model_path_constructed)}'")
         print(f"  3. The script's location relative to the 'models' directory is as expected.")
         print("-------------------\n")
    else:
        try:
            # Load the bot ONCE before the loop
            start_load_time = time.time()
            bot = nn_Bot(model_path=model_path_constructed)
            load_duration = time.time() - start_load_time
            print(f"Model loaded in {load_duration:.2f} seconds.")

            # --- Verify Model Dimensions Match Game ---
            # It's crucial the game dimensions used for testing match the model's expected input
            if bot.H > 0 and bot.W > 0: # Check if model dimensions were successfully inferred
                if (bot.H != GAME_HEIGHT or bot.W != GAME_WIDTH):
                    print(f"\n*** WARNING: Model was trained on {bot.H}x{bot.W} but testing with {GAME_HEIGHT}x{GAME_WIDTH}. ***")
                    print("*** Results may be unreliable. Consider changing GAME_HEIGHT/GAME_WIDTH to match the model. ***")
                    # Optionally, force game dimensions to match model for reliable testing:
                    # print(f"*** Adjusting game dimensions to {bot.H}x{bot.W} to match model for this test run. ***\n")
                    # GAME_HEIGHT = bot.H
                    # GAME_WIDTH = bot.W
                else:
                     print(f"Game dimensions ({GAME_HEIGHT}x{GAME_WIDTH}) match model's expected input.")
            else:
                 print("\n*** WARNING: Could not determine model's expected input dimensions. Cannot verify game size match. ***\n")


            # --- Gameplay Statistics ---
            wins = 0
            losses = 0
            errors = 0 # Track errors during gameplay (bot errors or game errors)
            total_games_attempted = 0
            start_run_time = time.time()

            print(f"\n--- Starting Test Run: {NUM_GAMES_TO_TEST} Games ({GAME_HEIGHT}x{GAME_WIDTH}, {NUM_MINES} mines) ---")
            # Use tqdm for a progress bar showing game progress
            for i in tqdm(range(NUM_GAMES_TO_TEST), desc="Playing Games", unit="game"):
                total_games_attempted += 1
                # Create a FRESH game instance for each run
                game = MineSweeper(GAME_HEIGHT, GAME_WIDTH, NUM_MINES)
                # game.start() # start() is called within play_game if needed

                # Play the game using the bot
                result = bot.play_game(game, verbose=VERBOSE_PLAY) # Pass verbose flag

                # Track statistics based on the result string
                if result == "win":
                    wins += 1
                elif result == "loss":
                    losses += 1
                else: # Catches "error" and "bot_error"
                    errors += 1
                    if VERBOSE_PLAY: print(f"Game {i+1} resulted in an error: {result}")


            end_run_time = time.time()
            duration = end_run_time - start_run_time
            games_completed = wins + losses # Only count games that finished normally

            # --- Print Final Statistics ---
            print("\n--- Test Run Finished ---")
            print(f"Total Games Attempted: {total_games_attempted}")
            print(f"Games Completed (Win/Loss): {games_completed}")
            print(f"Wins: {wins}")
            print(f"Losses: {losses}")
            if errors > 0:
                print(f"Errors/Bot Failures: {errors}")

            if games_completed > 0: # Calculate win rate based on completed games
                win_rate = (wins / games_completed) * 100
                print(f"Win Rate (of completed games): {win_rate:.2f}%")
            else:
                 print("Win Rate: N/A (No games completed without error)")

            if duration > 0:
                 games_per_sec = total_games_attempted / duration
                 print(f"Test Speed: {games_per_sec:.2f} games/sec")
            print(f"Total Test Time: {duration:.2f} seconds")

        except FileNotFoundError as e:
            # This might be redundant if the check above works, but good practice
            print(f"Error loading model: {e}")
        except Exception as e:
            print(f"\n--- An unexpected error occurred during bot initialization or the test run ---")
            traceback.print_exc()
            print("--------------------------------------------------------------------------------")
