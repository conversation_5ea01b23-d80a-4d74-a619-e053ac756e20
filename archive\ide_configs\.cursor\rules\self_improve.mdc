---
description: Guidelines for continuously improving Cursor rules based on emerging code patterns and best practices.
globs: **/*
alwaysApply: true
---

- **Rule Improvement Triggers:**
  - New code patterns not covered by existing rules
  - Repeated similar implementations across files
  - Common error patterns that could be prevented
  - New libraries or tools being used consistently
  - Emerging best practices in the codebase

- **Analysis Process:**
  - Compare new code with existing rules
  - Identify patterns that should be standardized
  - Look for references to external documentation
  - Check for consistent error handling patterns
  - Monitor test patterns and coverage

- **Rule Updates:**
  - **Add New Rules When:**
    - A new technology/pattern is used in 3+ files
    - Common bugs could be prevented by a rule
    - Code reviews repeatedly mention the same feedback
    - New security or performance patterns emerge

  - **Modify Existing Rules When:**
    - Better examples exist in the codebase
    - Additional edge cases are discovered
    - Related rules have been updated
    - Implementation details have changed

- **Example Pattern Recognition:**
  ```typescript
  // If you see repeated patterns like:
  const data = await prisma.user.findMany({
    select: { id: true, email: true },
    where: { status: 'ACTIVE' }
  });
  
  // Consider adding to [prisma.mdc](mdc:.cursor/rules/prisma.mdc):
  // - Standard select fields
  // - Common where conditions
  // - Performance optimization patterns
  ```

- **Rule Quality Checks:**
  - Rules should be actionable and specific
  - Examples should come from actual code
  - References should be up to date
  - Patterns should be consistently enforced

- **Continuous Improvement:**
  - Monitor code review comments
  - Track common development questions
  - Update rules after major refactors
  - Add links to relevant documentation
  - Cross-reference related rules

- **Rule Deprecation:**
  - Mark outdated patterns as deprecated
  - Remove rules that no longer apply
  - Update references to deprecated rules
  - Document migration paths for old patterns

- **Documentation Updates:**
  - Keep examples synchronized with code
  - Update references to external docs
  - Maintain links between related rules
  - Document breaking changes

- **Minesweeper Neural Network Improvement Patterns:**
  - **Performance Pattern Monitoring:**
    - Track model performance across different board configurations
    - Identify patterns where the model consistently underperforms
    - Document board configurations that cause model failures
    - Look for common features in successfully solved boards
  
  - **Neural Network Architecture Refinement:**
    ```python
    # If you observe patterns like:
    # Model performs well on small boards but fails on larger ones
    
    # Consider architecture modifications:
    class ImprovedMinesweeperCNN(nn.Module):
        def __init__(self):
            super().__init__()
            # Add multi-scale processing with different kernel sizes
            self.conv_small = nn.Conv2d(input_channels, 32, kernel_size=3, padding=1)
            self.conv_medium = nn.Conv2d(input_channels, 32, kernel_size=5, padding=2)
            self.conv_large = nn.Conv2d(input_channels, 32, kernel_size=7, padding=3)
            # Add additional improvement features...
    ```
  
  - **Input Representation Evolution:**
    - Track which input features contribute most to model decisions
    - Test adding derived features (e.g., probability estimates)
    - Experiment with different encodings for the same information
    - Document performance impact of representation changes
  
  - **Training Strategy Improvement:**
    - Monitor learning curves for signs of overfitting or underfitting
    - Adjust curriculum learning parameters based on performance
    - Test different reward structures in reinforcement learning
    - Compare performance of models trained with different strategies

- **Minesweeper-Specific Quality Checks:**
  - **Model Validation:**
    - Test on a statistically significant number of games (>=1000)
    - Verify performance across all difficulty levels
    - Compare against both logic bot and previous model versions
    - Validate on edge cases (e.g., very high mine density)
  
  - **Code Quality:**
    - Environment should accurately implement Minesweeper rules
    - Logic bot should follow the prescribed algorithm
    - Data generation should produce diverse and representative samples
    - Model implementations should follow best practices for neural networks
  
  - **Implementation Checks:**
    - Neural network should not contain hardcoded game logic
    - Input should only include information available to a player
    - Performance metrics should be consistent across evaluations
    - Variable size models should truly work on arbitrary board sizes

- **Continuous Neural Network Improvement:**
  - Review recent papers on reinforcement learning and neural networks
  - Experiment with novel architectures and training approaches
  - Cross-apply techniques from related domains (e.g., image recognition)
  - Maintain benchmark suite to compare model improvements

- **Documentation Requirements:**
  - Track all model architectures and their performance metrics
  - Document specific scenarios where neural models outperform logic
  - Maintain visualizations of model behavior and decision patterns
  - Update code examples when better implementations are discovered

Refer to [minesweeper.mdc](mdc:.cursor/rules/minesweeper.mdc) for complete project requirements and guidelines.

Follow [cursor_rules.mdc](mdc:.cursor/rules/cursor_rules.mdc) for proper rule formatting and structure.