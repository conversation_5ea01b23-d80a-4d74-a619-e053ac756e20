#!/usr/bin/env python3
"""
Configuration for Easy Difficulty Training (9x9, 10 mines)
Optimized parameters based on difficulty analysis.
"""

from ..base_trainer import TrainingConfig


class EasyConfig(TrainingConfig):
    """Configuration for easy difficulty training"""
    
    # Model identification
    model_arch: str = "SimpleCNN"
    difficulty: str = "easy"
    
    # Board configuration
    board_height: int = 9
    board_width: int = 9
    num_mines: int = 10
    
    # Training hyperparameters - optimized for easy difficulty
    batch_size: int = 512  # Higher batch size for simple patterns
    epochs: int = 25
    learning_rate: float = 0.0005  # Conservative learning rate
    validation_split: float = 0.15
    
    # Callback configuration
    early_stopping_patience: int = 8  # Standard patience for easy
    early_stopping_monitor: str = "val_move_accuracy"
    model_checkpoint_monitor: str = "val_move_accuracy"
    tensorboard_histogram_freq: int = 1
    
    # Data processing
    generator_chunk_size: int = 1024
    shuffle_buffer_size: int = 5000
    
    # Data file pattern for auto-detection
    data_file_pattern: str = "*easy*H9*W9*M10*.h5"
    
    # Directory configuration
    models_dir: str = "models/trained_simple"
    logs_dir: str = "logs/simple_training"
