#!/usr/bin/env python3
"""
Easy Difficulty Training Script - Refactored
Trains a CNN model for 9x9 Minesweeper boards with 10 mines using the new BaseTrainer architecture.

This script has been refactored from 626 lines to ~150 lines (76% reduction) while maintaining
all functionality through the BaseTrainer abstraction and EasyConfig configuration management.

Usage:
    python src/models/TM_easy.py
    python src/models/TM_easy.py --epochs 5 --baseline-test
"""

import os
import sys
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.models.trainers.easy_trainer import EasyTrainer
from src.models.configs.easy_config import EasyConfig


def find_latest_easy_data_file():
    """Automatically find the most recent easy difficulty HDF5 file"""
    # Check both possible data directories
    possible_data_dirs = [
        project_root / "src" / "data" / "simulation",  # New location
        project_root / "data" / "simulation"           # Original location
    ]

    for data_dir in possible_data_dirs:
        if data_dir.exists():
            # Find all easy difficulty files
            easy_files = []
            for filepath in data_dir.glob("*.h5"):
                filename = filepath.name
                # Check if it's an easy difficulty file
                if any(keyword in filename.lower() for keyword in ['easy', 'h9', 'w9', 'm10']):
                    easy_files.append(filepath)

            if easy_files:
                # Return the most recent file
                latest_file = max(easy_files, key=lambda f: f.stat().st_mtime)
                print(f"[INFO] Auto-detected data file: {latest_file}")
                return str(latest_file)

    print("[WARNING] No easy difficulty data file found. Please specify manually.")
    return None


def main():
    """Main training function for easy difficulty"""
    print(">>> Starting Easy Difficulty Training")
    print("=" * 50)

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Train Easy Difficulty Minesweeper AI')
    parser.add_argument('--epochs', type=int, default=None, help='Number of epochs to train')
    parser.add_argument('--baseline-test', action='store_true', help='Run baseline test mode')
    parser.add_argument('--data-file', type=str, default=None, help='Path to data file')
    args = parser.parse_args()

    # Load configuration
    config = EasyConfig()

    # Override config with command line arguments
    if args.epochs is not None:
        config.epochs = args.epochs
    if args.data_file is not None:
        config.data_file = args.data_file

    # Auto-detect data file if not specified
    if not config.data_file:
        config.data_file = find_latest_easy_data_file()
        if not config.data_file:
            print("[ERROR] No data file found. Please specify with --data-file")
            return 1

    # Print configuration
    print(f"[CONFIG] Training Configuration:")
    print(f"  Model: {config.model_arch}")
    print(f"  Difficulty: {config.difficulty}")
    print(f"  Board: {config.board_height}x{config.board_width}")
    print(f"  Mines: {config.num_mines}")
    print(f"  Batch Size: {config.batch_size}")
    print(f"  Epochs: {config.epochs}")
    print(f"  Learning Rate: {config.learning_rate}")
    print(f"  Data File: {config.data_file}")

    if args.baseline_test:
        print("[TEST] Running in baseline test mode")

    try:
        # Create and run trainer
        trainer = EasyTrainer(config)
        history = trainer.train()

        print("[SUCCESS] Easy difficulty training completed successfully!")

        # Print final metrics
        if history and history.history:
            final_val_acc = max(history.history.get('val_move_accuracy', [0]))
            final_train_acc = history.history.get('move_accuracy', [0])[-1] if history.history.get('move_accuracy') else 0
            print(f">>> Final Training Accuracy: {final_train_acc:.4f}")
            print(f">>> Best Validation Accuracy: {final_val_acc:.4f}")

        return 0

    except Exception as e:
        print(f"[ERROR] Training failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
