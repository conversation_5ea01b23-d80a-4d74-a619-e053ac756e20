#!/usr/bin/env python3
"""
Easy Trainer: Trainer for easy difficulty (9x9, 10 mines)
Implements task-specific model architecture and training logic.
"""

import tensorflow as tf
from typing import List
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.models.base_trainer import BaseTrainer


class EasyTrainer(BaseTrainer):
    """Trainer for easy difficulty (9x9, 10 mines)"""
    
    def create_model(self) -> tf.keras.Model:
        """Create CNN model optimized for easy difficulty"""
        H, W = self.config.board_height, self.config.board_width
        
        print(f"🏗️  Creating {self.config.model_arch} model for {H}x{W} board")
        
        model = tf.keras.Sequential([
            # Input layer - 12 channels for board state representation
            tf.keras.layers.Conv2D(
                32, (3, 3), 
                activation='relu', 
                input_shape=(H, W, 12),
                padding='same',
                name='conv2d_1'
            ),
            tf.keras.layers.BatchNormalization(name='batch_norm_1'),
            
            # Second convolutional layer
            tf.keras.layers.Conv2D(
                64, (3, 3), 
                activation='relu',
                padding='same',
                name='conv2d_2'
            ),
            tf.keras.layers.BatchNormalization(name='batch_norm_2'),
            
            # Pooling layer
            tf.keras.layers.MaxPooling2D((2, 2), name='max_pool_1'),
            tf.keras.layers.Dropout(0.25, name='dropout_1'),
            
            # Third convolutional layer
            tf.keras.layers.Conv2D(
                128, (3, 3), 
                activation='relu',
                padding='same',
                name='conv2d_3'
            ),
            tf.keras.layers.BatchNormalization(name='batch_norm_3'),
            tf.keras.layers.Dropout(0.25, name='dropout_2'),
            
            # Flatten and dense layers
            tf.keras.layers.Flatten(name='flatten'),
            tf.keras.layers.Dense(256, activation='relu', name='dense_1'),
            tf.keras.layers.Dropout(0.5, name='dropout_3'),
            tf.keras.layers.Dense(128, activation='relu', name='dense_2'),
            tf.keras.layers.Dropout(0.3, name='dropout_4'),
            
            # Output layer - one neuron per board cell
            tf.keras.layers.Dense(H * W, activation='softmax', name='output')
        ], name=f'{self.config.model_arch}_Easy_{H}x{W}')
        
        # Print model summary
        print(f"📋 Model Summary:")
        model.summary()
        
        return model
    
    def get_loss_function(self):
        """Get loss function optimized for easy difficulty"""
        return tf.keras.losses.CategoricalCrossentropy(
            from_logits=False,
            label_smoothing=0.05,  # Light label smoothing for easy difficulty
            name='categorical_crossentropy'
        )
    
    def get_metrics(self) -> List:
        """Get metrics for easy difficulty evaluation"""
        from src.models.base_trainer import move_accuracy, top_k_move_accuracy
        return [
            move_accuracy,
            top_k_move_accuracy(k=3),
            top_k_move_accuracy(k=5),
            tf.keras.metrics.CategoricalCrossentropy(name='categorical_crossentropy_metric')
        ]
    
    def _get_sample_count(self) -> int:
        """Get sample count from data file for easy difficulty"""
        # Try to get actual count from data file
        if self.config.data_file:
            try:
                import h5py
                with h5py.File(self.config.data_file, 'r') as f:
                    if 'moves' in f:
                        return len(f['moves'])
                    elif 'X' in f:
                        return len(f['X'])
            except Exception:
                pass
        
        # Default estimate for easy difficulty
        return 50000  # Typical for easy difficulty training data
