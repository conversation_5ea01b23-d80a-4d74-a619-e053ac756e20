from enum import Enum
from random import sample, choice, random
from collections import deque
import numpy as np

# Assume GameSymbol is defined in a separate module.
from . import GameSymbol

class Inference(str, Enum):
    SAFE = "SAFE"
    NONE = "NONE"
    MINE = "MINE"

    def __str__(self):
        return GameSymbol[self.value]

    def __repr__(self):
        return self.value

class MineSweeper:
    MINE = -2
    UNREVEALED = -1

    def __init__(self, H=9, W=9, M=10):
        # Ensure M is not too large
        if M >= H * W:
            raise ValueError(f"Number of mines ({M}) cannot be equal or greater than total cells ({H*W})")
        if M < 0:
             raise ValueError(f"Number of mines ({M}) cannot be negative")

        self.H = H
        self.W = W
        self.M = M
        self.game_over = False
        self.win_condition_met = False # Added flag

        # Initialize mine positions once.
        mine_positions = sample(range(H * W), M)
        self.mines = [[False for _ in range(W)] for _ in range(H)]
        for pos in mine_positions:
            r, c = divmod(pos, W)
            self.mines[r][c] = True

        # Compute the grid (ground truth) using the same mine configuration.
        # Use count_adjacent_mines directly here.
        self.grid = [[0 for _ in range(W)] for _ in range(H)]
        for r in range(H):
            for c in range(W):
                if self.mines[r][c]:
                    self.grid[r][c] = self.MINE
                else:
                    # Use the standard method now
                    self.grid[r][c] = self.count_adjacent_mines(r, c)

        # Initialize board state arrays.
        self.revealed = [[False for _ in range(W)] for _ in range(H)]
        self.flagged = [[False for _ in range(W)] for _ in range(H)]
        # inference board might be primarily for display or external bot use
        self.inference = [[Inference.NONE for _ in range(W)] for _ in range(H)]
        self._mines_count = [[self.UNREVEALED for _ in range(W)] for _ in range(H)]
        self.mines_triggered_count = 0 # Track triggered mines

    # Removed count_adjacent_mines_grid as it was redundant

    def count_adjacent_mines(self, x, y):
        """Counts adjacent mines using helper method _is_valid_cell."""
        count = 0
        for i in range(-1, 2):
            for j in range(-1, 2):
                if (i, j) == (0, 0):
                    continue
                nx, ny = x + i, y + j
                # Use _is_valid_cell for boundary check
                if self._is_valid_cell(nx, ny) and self.mines[nx][ny]:
                    count += 1
        return count

    def _is_valid_cell(self, x, y):
        """Checks if (x, y) is within board bounds."""
        return 0 <= x < self.H and 0 <= y < self.W

    def update_grid(self):
        """Recomputes the grid (mine and clue numbers) based on current mines.
           Needed after mine relocation."""
        for r in range(self.H):
            for c in range(self.W):
                if self.mines[r][c]:
                    self.grid[r][c] = self.MINE
                else:
                    self.grid[r][c] = self.count_adjacent_mines(r, c)

    def _replace_mine(self, excluded_cells: set):
        """
        Replaces a removed mine by placing a new mine at a random location
        that is not in the excluded zone.

        Raises:
            RuntimeError: If no valid location can be found for mine placement
        """
        placed = False
        possible_locations = list(range(self.H * self.W))
        # sample(possible_locations, len(possible_locations)) # Shuffle for randomness
        # random.shuffle(possible_locations)
        possible_locations = sample(possible_locations, len(possible_locations))

        for new_location in possible_locations:
            x, y = divmod(new_location, self.W)
            # Ensure it's not an existing mine and not in the excluded zone
            if not self.mines[x][y] and (x, y) not in excluded_cells:
                self.mines[x][y] = True
                placed = True
                break

        if not placed:
            # Calculate available space for debugging
            total_cells = self.H * self.W
            excluded_count = len(excluded_cells)
            existing_mines = sum(sum(row) for row in self.mines)
            available_cells = total_cells - excluded_count - existing_mines

            error_msg = (f"Critical Error: Could not replace mine! "
                        f"Board: {self.H}x{self.W} ({total_cells} cells), "
                        f"Excluded: {excluded_count}, Existing mines: {existing_mines}, "
                        f"Available cells: {available_cells}")

            # Log critical error and raise exception to prevent data corruption
            print(f"CRITICAL: {error_msg}")
            raise RuntimeError(error_msg)


    def _make_start_safe(self, x, y):
        """
        Ensures that the starting cell (x, y) and its immediate neighbors are free of mines.
        Any mines in the 3x3 safe zone are removed and replaced elsewhere.
        Updates the grid afterwards.
        """
        safe_cells_coords = set()
        mines_to_relocate = 0
        for i in range(x - 1, x + 2):
            for j in range(y - 1, y + 2):
                if self._is_valid_cell(i, j):
                    safe_cells_coords.add((i, j))
                    if self.mines[i][j]:
                        self.mines[i][j] = False
                        mines_to_relocate += 1

        # Relocate the removed mines
        for _ in range(mines_to_relocate):
            self._replace_mine(safe_cells_coords)

        # Recompute the ground truth grid after mine relocation
        self.update_grid()

    def start(self):
        """
        Starts the game by revealing a random safe cell, ensuring the start is safe.
        """
        # Choose a truly random cell first
        start_pos = choice(range(self.H * self.W))
        x, y = divmod(start_pos, self.W)

        # Make this cell and its neighbors safe
        self._make_start_safe(x, y)

        # Now reveal the guaranteed safe starting cell (x, y)
        # The reveal needs to happen *after* make_start_safe updates the grid
        return self.reveal(x, y)


    def reveal(self, x, y):
        """
        Reveals the cell at (x, y) and returns a tuple:
        (cell_value, win_condition_met, mine_triggered_this_step).
        MODIFIED: Sets game_over = True immediately if a mine is triggered.
        """
        # --- (Initial checks for game_over, validity, revealed, flagged remain the same) ---
        if self.game_over or not self._is_valid_cell(x,y) or self.revealed[x][y] or self.flagged[x][y]:
             value = self._mines_count[x][y] if self._is_valid_cell(x,y) and self.revealed[x][y] else self.UNREVEALED
             return value, self.win_condition_met, False

        self.revealed[x][y] = True
        mine_triggered_this_step = False

        # Use the pre-computed grid value
        cell_value = self.grid[x][y]
        self._mines_count[x][y] = cell_value # Update the visible board state

        if cell_value == self.MINE:
            mine_triggered_this_step = True
            self.mines_triggered_count += 1
            # --- ADD THIS LINE ---
            self.game_over = True # End the game immediately on mine trigger
            # --- END ADDITION ---
        else:
            # Cascade reveal if this cell has 0 adjacent mines.
            if cell_value == 0:
                self.cascade_reveal(x, y)

        # Check for win condition *if game isn't already over*
        # Win condition only matters if a mine didn't end it first
        if not self.game_over:
            self.win_condition_met = self.check_win_condition()
            if self.win_condition_met:
                self.game_over = True # Win condition also ends the game

        return cell_value, self.win_condition_met, mine_triggered_this_step


    def cascade_reveal(self, x, y):
        """Performs a BFS-based cascade reveal for cells with 0 adjacent mines."""
        queue = deque([(x, y)])
        visited = set([(x,y)]) # Prevent re-processing in cascade

        while queue:
            cx, cy = queue.popleft()

            for i in range(-1, 2):
                for j in range(-1, 2):
                    if (i, j) == (0, 0):
                        continue
                    nx, ny = cx + i, cy + j

                    if self._is_valid_cell(nx, ny) and (nx, ny) not in visited and not self.revealed[nx][ny] and not self.flagged[nx][ny]:
                        visited.add((nx, ny)) # Mark as visited for this cascade
                        self.revealed[nx][ny] = True
                        cell_value = self.grid[nx][ny] # Use pre-computed grid
                        self._mines_count[nx][ny] = cell_value

                        if cell_value == 0:
                            queue.append((nx, ny))

    def flag_cell(self, x, y):
        """Toggles the flagged state of the cell at (x, y)."""
        if self._is_valid_cell(x,y) and not self.revealed[x][y]:
            self.flagged[x][y] = not self.flagged[x][y]

    def make_move(self, row, col):
        """
        Processes a reveal move at the specified cell. Simplified for external callers.
        Returns dict: {value, won, mine_triggered, game_over, board_state}
        NOTE: game_over status reflects immediate end on mine trigger if reveal is modified.
        """
        # --- (Initial checks remain the same) ---
        if not self._is_valid_cell(row, col):
            raise ValueError(f"Invalid move coordinates ({row}, {col}).")
        if self.revealed[row][col] or self.flagged[row][col]:
            return { # Return current state for invalid move
                 "value": self._mines_count[row][col] if self.revealed[row][col] else self.UNREVEALED,
                 "won": self.win_condition_met,
                 "mine_triggered": False,
                 "game_over": self.game_over, # Reflects current state
                 "board_state": self.get_current_state(),
                 "invalid_move": True
            }

        # --- Call the modified reveal method ---
        value, won, mine_triggered = self.reveal(row, col)

        # The game_over status is now correctly set within reveal
        return {
            "value": value,
            "won": won, # win_condition_met
            "mine_triggered": mine_triggered,
            "game_over": self.game_over, # Reflects state after reveal
            "board_state": self.get_current_state(), # Return updated state
            "invalid_move": False
        }

    def get_result(self):
        """Returns the final result of the game: win, loss (if ended by mine), or ongoing."""
        if self.win_condition_met:
            return "win"
        if self.game_over: # Game ended but not by win, must be loss (or forced end)
             return "loss" # Assuming game_over is set appropriately when a mine ends the game
        return "ongoing"

    def check_win_condition(self):
        """
        Checks whether all non-mine cells have been revealed.
        Returns True if won, False otherwise. Does NOT set game_over here.
        """
        revealed_count = np.sum(self.revealed)
        total_cells = self.H * self.W
        safe_cells = total_cells - self.M
        return revealed_count == safe_cells

    def reveal_all(self):
        """Reveals all cells on the board (e.g., for display at game end)."""
        for x in range(self.H):
            for y in range(self.W):
                if not self.revealed[x][y]:
                    # Don't change game state flags here, just reveal for display
                    if self.mines[x][y]:
                        self._mines_count[x][y] = self.MINE
                        self.inference[x][y] = Inference.MINE # Mark inference for display
                    else:
                        self._mines_count[x][y] = self.grid[x][y] # Use pre-computed clue
                        self.inference[x][y] = Inference.SAFE # Mark inference for display
                # Mark already revealed cells based on their value
                elif self._mines_count[x][y] >= 0:
                     self.inference[x][y] = Inference.SAFE
                elif self._mines_count[x][y] == self.MINE:
                     self.inference[x][y] = Inference.MINE


    def mark_safe(self, x, y):
        """Marks cell (x, y) as safe in the inference board (for display/external bot)."""
        if self._is_valid_cell(x,y):
            self.inference[x][y] = Inference.SAFE

    def mark_mine(self, x, y):
        """Marks cell (x, y) as a mine in the inference board (for display/external bot)."""
        if self._is_valid_cell(x,y):
            self.inference[x][y] = Inference.MINE

    def get_current_state(self):
        """Returns a simplified HxW list-of-lists representation of the board's current visible state."""
        state = [[self.UNREVEALED for _ in range(self.W)] for _ in range(self.H)]
        for r in range(self.H):
            for c in range(self.W):
                if self.revealed[r][c]:
                    state[r][c] = self._mines_count[r][c]
                elif self.flagged[r][c]:
                    state[r][c] = 'F' # Represent flag
                # else: leave as UNREVEALED
        return state

    def __str__(self):
        """Returns a string representation using GameSymbol and inference board."""
        result = ""
        for i in range(self.H):
            for j in range(self.W):
                if self.revealed[i][j]:
                    result += str(GameSymbol[self._mines_count[i][j]])
                elif self.flagged[i][j]:
                    result += str(GameSymbol["MINE"]) # Show flag symbol
                else:
                    # Show inference if available, otherwise default unrevealed
                    result += str(GameSymbol[self.inference[i][j]])
            result += "\n"
        return result

    @property
    def remaining_unrevealed_cells(self):
        """Returns the count of unrevealed, unflagged cells."""
        # Use faster numpy implementation
        return self.H * self.W - np.sum(self.revealed) - np.sum(self.flagged)


    def get_board_state_for_nn(self):
        """
        Converts the current game state to a neural network input representation.
        The output is an H x W x C array, where C=12 channels:
          - Channel 0: Revealed indicator (1 if revealed, 0 otherwise).
          - Channels 1-9: One-hot encoding of clue numbers 0–8. (Clue 0 -> Ch 1, Clue 8 -> Ch 9)
          - Channel 10: Mask for unrevealed cells (1 if unrevealed and not flagged, 0 otherwise).
          - Channel 11: Normalized total mine count (M / (H * W)).
        """
        H, W, C = self.H, self.W, 12
        input_array = np.zeros((H, W, C), dtype=np.float32) # Use float32 for TF/PyTorch

        norm_mine_density = self.M / (H * W) if (H * W) > 0 else 0
        input_array[:, :, 11] = norm_mine_density # Fill the last channel

        for r in range(H):
            for c in range(W):
                if self.revealed[r][c]:
                    input_array[r, c, 0] = 1.0  # Revealed indicator.
                    clue = self._mines_count[r][c]
                    if clue >= 0: # Clues 0-8
                        # One-hot encode clue: clue 'c' goes into channel 1+c
                        input_array[r, c, 1 + clue] = 1.0
                elif not self.flagged[r][c]: # Only mask if unrevealed AND not flagged
                    input_array[r, c, 10] = 1.0  # Unrevealed mask.

        return input_array

    def execute_nn_action(self, action_index):
        """
        Executes an action provided by a neural network.
        The action is assumed to be an integer index into the flattened board (row-major).
        """
        if not (0 <= action_index < self.H * self.W):
             raise ValueError(f"Invalid action index {action_index} for board size {self.H}x{self.W}")
        x, y = divmod(action_index, self.W)
        # Use the standard make_move method to handle game logic
        return self.make_move(x, y)

    @property
    def values(self):
        """
        Returns a copy of the current clue/mine count grid visible to the player.
        """
        return [row[:] for row in self._mines_count]

# --- End of Modified MineSweeper Class ---