#!/usr/bin/env python3
"""
Test script to verify the bug fixes implemented in the Minesweeper data generation pipeline.

This script tests:
1. MineSweeper._replace_mine error handling
2. BayesBot state consistency 
3. SimpleLogicBot enhanced random selection
"""

import sys
import os
import numpy as np
from typing import Set, Tuple

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.game.MineSweeper import MineSweeper
from src.bots.SimpleLogicBot import SimpleLogicBot
from src.bots.BayesBot import BayesBot


def test_minesweeper_replace_mine_error_handling():
    """Test that _replace_mine properly handles failure cases"""
    print("Testing MineSweeper._replace_mine error handling...")
    
    # Create a scenario where mine replacement should fail
    # Small board with many mines and large exclusion zone
    try:
        game = MineSweeper(H=3, W=3, M=8)  # 8 mines on 3x3 board
        
        # Create exclusion zone that covers most of the board
        excluded_cells = {(0,0), (0,1), (0,2), (1,0), (1,1), (1,2), (2,0), (2,1)}
        
        # This should raise RuntimeError due to insufficient space
        try:
            game._replace_mine(excluded_cells)
            print("❌ FAIL: Expected RuntimeError was not raised")
            return False
        except RuntimeError as e:
            print(f"✅ PASS: RuntimeError properly raised: {str(e)[:100]}...")
            return True
            
    except Exception as e:
        print(f"❌ FAIL: Unexpected error during test: {e}")
        return False


def test_bayesbot_state_consistency():
    """Test that BayesBot maintains consistent state between inferred_safe and remaining_cells"""
    print("Testing BayesBot state consistency...")
    
    try:
        # Create a simple game scenario
        game = MineSweeper(H=5, W=5, M=5)
        game.start()
        
        # Create BayesBot
        bot = BayesBot(game)
        
        # Simulate some moves to create inferences
        for _ in range(3):
            move = bot.make_move(game)
            if move is None:
                break
            result = game.make_move(move[0], move[1])
            if result.get("mine_triggered", False) or result.get("won", False):
                break
        
        # Check state consistency
        inconsistencies = []
        
        # Check that inferred safe cells are not in remaining_cells
        for (r, c) in bot.inferred_safe:
            if bot.remaining_cells[r, c]:
                inconsistencies.append(f"Inferred safe cell ({r},{c}) still in remaining_cells")
        
        # Check that inferred mine cells are not in remaining_cells  
        for (r, c) in bot.inferred_mine:
            if bot.remaining_cells[r, c]:
                inconsistencies.append(f"Inferred mine cell ({r},{c}) still in remaining_cells")
        
        # Check that revealed cells are not in remaining_cells
        for r in range(game.H):
            for c in range(game.W):
                if game.revealed[r][c] and bot.remaining_cells[r, c]:
                    inconsistencies.append(f"Revealed cell ({r},{c}) still in remaining_cells")
        
        if inconsistencies:
            print("❌ FAIL: State inconsistencies found:")
            for issue in inconsistencies:
                print(f"  - {issue}")
            return False
        else:
            print("✅ PASS: BayesBot state is consistent")
            return True
            
    except Exception as e:
        print(f"❌ FAIL: Error during BayesBot test: {e}")
        return False


def test_simplelogicbot_enhanced_selection():
    """Test that SimpleLogicBot uses enhanced random selection heuristics"""
    print("Testing SimpleLogicBot enhanced random selection...")
    
    try:
        # Create a game with some revealed areas
        game = MineSweeper(H=10, W=10, M=15)
        game.start()
        
        # Reveal a few cells to create some clues
        for _ in range(5):
            # Find an unrevealed cell and reveal it
            for r in range(game.H):
                for c in range(game.W):
                    if not game.revealed[r][c]:
                        result = game.make_move(r, c)
                        if result.get("mine_triggered", False):
                            continue
                        break
                else:
                    continue
                break
        
        # Create SimpleLogicBot
        bot = SimpleLogicBot(game.H, game.W, game.M)
        bot.update_from_game(game)
        bot.run_inference_loop()
        
        # Test the enhanced selection multiple times
        selections = []
        available_cells = list(bot.cells_remaining - bot.inferred_mine)
        
        if len(available_cells) > 5:  # Only test if we have enough cells
            # Test the heuristic selection function directly
            for _ in range(20):
                selected = bot._select_random_with_heuristics(available_cells)
                if selected:
                    selections.append(selected)
            
            if selections:
                print(f"✅ PASS: Enhanced selection working, made {len(selections)} selections")
                
                # Check if selections show some preference for corners/edges
                corner_edge_count = 0
                for r, c in selections:
                    if (r == 0 or r == game.H-1 or c == 0 or c == game.W-1):
                        corner_edge_count += 1
                
                edge_preference = corner_edge_count / len(selections)
                print(f"  - Edge/corner preference: {edge_preference:.2f} ({corner_edge_count}/{len(selections)})")
                return True
            else:
                print("❌ FAIL: No selections made")
                return False
        else:
            print("✅ PASS: Enhanced selection method exists (insufficient cells for full test)")
            return True
            
    except Exception as e:
        print(f"❌ FAIL: Error during SimpleLogicBot test: {e}")
        return False


def test_data_generation_integration():
    """Test that the fixes don't break the data generation pipeline"""
    print("Testing data generation integration...")
    
    try:
        # Import the simulation function
        from src.simulations import play_proba
        
        # Test with SimpleLogicBot
        game = MineSweeper(H=9, W=9, M=10)
        bot = SimpleLogicBot(game.H, game.W, game.M)
        
        result = play_proba(game, bot, verbose=False)
        
        if result and len(result) >= 2:
            states, actions = result[:2]
            if len(states) > 0 and len(actions) > 0:
                print("✅ PASS: Data generation with SimpleLogicBot works")
            else:
                print("❌ FAIL: No data generated")
                return False
        else:
            print("❌ FAIL: Invalid result from play_proba")
            return False
        
        # Test with BayesBot
        game2 = MineSweeper(H=9, W=9, M=10)
        bot2 = BayesBot(game2)
        
        result2 = play_proba(game2, bot2, verbose=False)
        
        if result2 and len(result2) >= 2:
            states2, actions2 = result2[:2]
            if len(states2) > 0 and len(actions2) > 0:
                print("✅ PASS: Data generation with BayesBot works")
                return True
            else:
                print("❌ FAIL: No data generated with BayesBot")
                return False
        else:
            print("❌ FAIL: Invalid result from play_proba with BayesBot")
            return False
            
    except Exception as e:
        print(f"❌ FAIL: Error during integration test: {e}")
        return False


def main():
    """Run all bug fix tests"""
    print("🔍 Running Bug Fix Verification Tests")
    print("=" * 50)
    
    tests = [
        test_minesweeper_replace_mine_error_handling,
        test_bayesbot_state_consistency,
        test_simplelogicbot_enhanced_selection,
        test_data_generation_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        print()
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ FAIL: Test {test_func.__name__} crashed: {e}")
    
    print()
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All bug fixes verified successfully!")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the fixes.")
        return 1


if __name__ == "__main__":
    exit(main())
