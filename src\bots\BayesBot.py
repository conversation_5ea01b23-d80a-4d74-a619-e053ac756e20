from collections import deque
import numpy as np
from typing import List, <PERSON><PERSON>, Set, Deque, Dict, Optional
import copy
from random import choice
from src.game.MineSweeper import MineSweeper
import random

class BayesBot:
    STEPS = [(-1, -1), (-1, 0), (-1, 1), (0, 1),
             (1, 1), (1, 0), (1, -1), (0, -1)]

    def __init__(self, game: MineSweeper): # Type hint for game
        # Store only necessary game information, not the full game reference
        self.H = game.H
        self.W = game.W
        self.M = game.M  # Total number of mines

        # No longer store direct game reference
        self._game_state = None  # Will hold revealed state only
        self._revealed = [[False for _ in range(game.W)] for _ in range(game.H)]
        self._values = [[-1 for _ in range(game.W)] for _ in range(game.H)]  # Values visible to player

        # Initialize uniform probabilities for unrevealed cells.
        initial_prob = game.M / (game.H * game.W) if (game.H * game.W) > 0 else 0
        self._probabilities = np.full((game.H, game.W), initial_prob, dtype=float)

        # Track game state from bot's perspective
        self.remaining_cells = np.ones((game.H, game.W), dtype=bool) # Cells not yet revealed or inferred
        self.inferred_safe: Set[Tuple[int, int]] = set()
        self.inferred_mine: Set[Tuple[int, int]] = set()
        self.attempted_safe: Set[Tuple[int, int]] = set() # Track safe cells chosen by bot
        self.revealed_cells: Set[Tuple[int, int]] = set() # Track cells revealed by the game

        # Constraints: List of (neighbor_coords_list, clue_value)
        self.constraints: List[Tuple[List[Tuple[int, int]], int]] = []
        self.state_history: Deque[Dict] = deque(maxlen=5) # For potential backtracking

        # Update from initial game state
        self.update_from_game(game)

    def snapshot_state(self):
        """Take a snapshot of the current inference state."""
        state = {
            'inferred_safe': copy.deepcopy(self.inferred_safe),
            'inferred_mine': copy.deepcopy(self.inferred_mine),
            'attempted_safe': copy.deepcopy(self.attempted_safe),
            'remaining_cells': self.remaining_cells.copy(),
            'constraints': copy.deepcopy(self.constraints),
            '_probabilities': self._probabilities.copy(),
            'revealed_cells': copy.deepcopy(self.revealed_cells) # Also save revealed cells state
        }
        self.state_history.append(state)
        # print("State snapshot taken.") # Commented out verbose print

    def backtrack(self):
        """Restore the most recent snapshot state."""
        if self.state_history:
            state = self.state_history.pop()
            self.inferred_safe = state['inferred_safe']
            self.inferred_mine = state['inferred_mine']
            self.attempted_safe = state['attempted_safe']
            self.remaining_cells = state['remaining_cells']
            self.constraints = state['constraints']
            self._probabilities = state['_probabilities']
            self.revealed_cells = state['revealed_cells']
            # print("Backtracked to previous state.") # Commented out verbose print
            return True
        else:
            # print("No state available to backtrack.") # Commented out verbose print
            return False

    def check_consistency(self) -> bool:
        """
        Check if current inferences violate any known constraint.
        Constraint: (neighbor_cells, clue_value)
        Violation if: (clue - # known_mines_in_neighbors) > # remaining_unknown_neighbors
        Violation if: (# neighbors - clue - # known_safe_in_neighbors) > # remaining_unknown_neighbors
        """
        for cells, clue in self.constraints:
            known_mines_count = 0
            known_safe_count = 0
            unknown_neighbors = []

            for (r, c) in cells:
                if (r, c) in self.inferred_mine:
                    known_mines_count += 1
                elif (r, c) in self.inferred_safe or self._revealed[r][c]: # Treat revealed as safe here
                    known_safe_count += 1
                elif self.remaining_cells[r,c]: # Only count truly unknown neighbors
                    unknown_neighbors.append((r, c))

            num_unknown = len(unknown_neighbors)
            total_neighbors = len(cells)

            # Check 1: Too many mines required for remaining unknowns
            if clue - known_mines_count > num_unknown:
                # print(f"Inconsistency (Too many mines needed): Clue={clue}, KnownMines={known_mines_count}, Unknown={num_unknown}, Neighbors={cells}")
                return False

            # Check 2: Too many safe cells required for remaining unknowns
            if (total_neighbors - clue) - known_safe_count > num_unknown:
                # print(f"Inconsistency (Too many safe needed): Clue={clue}, KnownSafe={known_safe_count}, Unknown={num_unknown}, Neighbors={cells}")
                return False

        # Global check: total inferred mines shouldn't exceed game mines
        if len(self.inferred_mine) > self.M:
            # print(f"Inconsistency (Global): Inferred mines {len(self.inferred_mine)} > Total mines {self.M}")
            return False

        return True


    def update_from_game(self, game):
        """ Update bot's knowledge based on the actual game revealed state.
            Only copy information that would be visible to a player. """
        newly_revealed = False

        # Store a copy of revealed cells and their values (not the game object itself)
        for r in range(self.H):
            for c in range(self.W):
                # Only update if revealed state has changed
                if game.revealed[r][c] and not self._revealed[r][c]:
                    self._revealed[r][c] = True
                    self._values[r][c] = game._mines_count[r][c]

                    self.revealed_cells.add((r,c))
                    # If revealed, it's no longer remaining and not a mine/safe inference target
                    self.remaining_cells[r, c] = False
                    if (r,c) in self.inferred_safe: self.inferred_safe.remove((r,c))
                    if (r,c) in self.inferred_mine: self.inferred_mine.remove((r,c)) # Should not happen if logic is correct

                    clue = self._values[r][c]
                    if clue >= 0: # It's a numbered cell, add constraint
                         neighbors = self.get_neighbors(r, c)
                         # Avoid adding duplicate constraints
                         if (neighbors, clue) not in self.constraints:
                              self.constraints.append((neighbors, clue))
                    newly_revealed = True

        return newly_revealed


    def basic_inference(self) -> bool:
        """
        Applies the two basic inference rules based on constraints.
        Returns True if any new inference was made.
        Rule 1: If (clue - # known_mines) == # unknown_neighbors, mark unknowns as mines.
        Rule 2: If (# total_neighbors - clue - # known_safe) == # unknown_neighbors, mark unknowns as safe.
        """
        made_inference = False
        for cells, clue in self.constraints:
            known_mines_count = 0
            known_safe_count = 0
            unknown_neighbors = []

            for (r, c) in cells:
                if (r, c) in self.inferred_mine:
                    known_mines_count += 1
                # Consider revealed cells as 'known safe' for this rule
                elif (r, c) in self.inferred_safe or self._revealed[r][c]:
                    known_safe_count += 1
                # Check remaining_cells status for true unknowns
                elif self.remaining_cells[r, c]:
                    unknown_neighbors.append((r, c))

            num_unknown = len(unknown_neighbors)
            total_neighbors = len(cells)

            if num_unknown == 0: # No unknowns, constraint satisfied or invalid
                continue

            # Rule 1: Infer Mines
            if clue - known_mines_count == num_unknown:
                if self._infer_mines(unknown_neighbors):
                    made_inference = True

            # Rule 2: Infer Safe
            # Number of mines among neighbors = clue
            # Number of safe among neighbors = total_neighbors - clue
            # Required safe = (total_neighbors - clue)
            # Known safe = known_safe_count
            if (total_neighbors - clue) - known_safe_count == num_unknown:
                if self._infer_safe_cells(unknown_neighbors):
                    made_inference = True

        return made_inference


    def predict_proba(self):
        """
        Update probabilities of each cell containing a mine.
        Returns a probability array of shape (H, W) with values between 0-1.
        """
        # FIXED: Calculate remaining unknown count correctly after state consistency fix
        # remaining_cells now only contains truly unknown cells (not inferred safe/mine)
        remaining_mines = max(0, self.M - len(self.inferred_mine))
        remaining_unknown_count = np.sum(self.remaining_cells)

        # Enhanced logging for debugging state consistency
        # Uncomment for debugging: print(f"DEBUG: Prior calculation - {remaining_mines} remaining mines / {remaining_unknown_count} remaining cells")

        # Avoid division by zero if no unknown cells left
        if remaining_unknown_count <= 0:
             prior = 0
        else:
             prior = remaining_mines / remaining_unknown_count
             # Clamp probability between 0 and 1, handle potential float issues
             prior = max(0.0, min(1.0, prior))
             # Uncomment for debugging: print(f"DEBUG: Calculated prior probability: {prior:.4f}")

        # Initialize probabilities: 0 for known safe/revealed, 1 for known mine, prior for unknown
        self._probabilities.fill(prior) # Default for unknown
        for r in range(self.H):
             for c in range(self.W):
                  if not self.remaining_cells[r,c]: # If not remaining (revealed, inferred safe/mine)
                       if (r,c) in self.inferred_mine:
                            self._probabilities[r,c] = 1.0
                       else: # Revealed or inferred safe
                            self._probabilities[r,c] = 0.0

        # Add small amount of noise to probabilities to simulate human uncertainty
        # This creates more diverse training data while maintaining the core Bayesian logic
        noise_scale = 0.05  # 5% noise level
        noise = np.random.normal(0, noise_scale, self._probabilities.shape)

        # Only apply noise to cells that aren't already determined (not 0 or 1)
        mask = (self._probabilities > 0) & (self._probabilities < 1)
        self._probabilities[mask] = np.clip(self._probabilities[mask] + noise[mask], 0.001, 0.999)

        return self._probabilities


    def repeated_inference_pass(self, verbose: bool = False):
        """
        Repeatedly apply inference rules until no new inferences occur.
        Updates based on game state first.
        """
        # print("DEBUG: Entering repeated_inference_pass") # DEBUG START
        # No longer call update_from_game here since it's called in make_move
        # print("DEBUG: update_from_game completed") # DEBUG

        iteration = 0 # DEBUG loop counter
        MAX_INFERENCE_ITERATIONS = self.H * self.W * 2 # Safety break limit

        while True:
            iteration += 1
            # print(f"DEBUG: Inference Pass Iteration {iteration}") # DEBUG

            made_change = False
            # print("DEBUG: Running basic_inference...") # DEBUG
            if self.basic_inference():
                made_change = True
                # print("DEBUG: basic_inference made changes.") # DEBUG
            # print("DEBUG: Running advanced_inference...") # DEBUG
            if self.advanced_inference():
                made_change = True
                # print("DEBUG: advanced_inference made changes.") # DEBUG
            # print("DEBUG: Running pattern_inference...") # DEBUG
            if self.pattern_inference(): # Currently returns False
                 made_change = True
                #  print("DEBUG: pattern_inference made changes.") # DEBUG


            # print(f"DEBUG: Iteration {iteration} complete. made_change={made_change}") # DEBUG

            # --- Check Exit Condition ---
            if not made_change:
                # print("DEBUG: No changes made, exiting inference loop.") # DEBUG
                break # Exit loop successfully

            # --- Safety Break ---
            if iteration > MAX_INFERENCE_ITERATIONS:
                #  print(f"ERROR: Exceeded max inference iterations ({MAX_INFERENCE_ITERATIONS}). Breaking loop.")
                 # Optionally: print current state for diagnosis
                 # print(f"DEBUG: Bot state - Safe: {self.inferred_safe}, Mines: {self.inferred_mine}, Remaining: {np.sum(self.remaining_cells)}")
                 break

        # print("DEBUG: Calling predict_proba after loop.") # DEBUG
        self.predict_proba()
        # print("DEBUG: Exiting repeated_inference_pass") # DEBUG END

    def pattern_inference(self) -> bool:
        """ Stub for additional inference patterns (e.g., 1-2 pattern). Returns True if changes made."""
        # Placeholder - can be expanded later
        return False

    def advanced_inference(self) -> bool:
        """ Subset rule implementation. Returns True if changes made. """
        changed = False
        constraints = self.constraints # Use current constraints

        # Pre-calculate unknowns for each constraint to avoid redundant computation
        constraint_unknowns = {}
        for i, (cells1, clue1) in enumerate(constraints):
            known_mines1 = sum(1 for c in cells1 if c in self.inferred_mine)
            unknown1 = {c for c in cells1 if self.remaining_cells[c[0], c[1]]}
            mines_needed1 = clue1 - known_mines1
            constraint_unknowns[i] = (unknown1, mines_needed1)

        for i in range(len(constraints)):
            unknown1, mines_needed1 = constraint_unknowns[i]
            if not unknown1: continue # Skip if no unknowns

            for j in range(i + 1, len(constraints)):
                unknown2, mines_needed2 = constraint_unknowns[j]
                if not unknown2: continue # Skip if no unknowns

                # Check subset relation: unknown1 < unknown2
                if unknown1.issubset(unknown2) and unknown1 != unknown2:
                    diff_set = unknown2 - unknown1
                    diff_mines = mines_needed2 - mines_needed1
                    num_diff = len(diff_set)

                    if num_diff > 0:
                        # If mines needed in diff == size of diff -> all are mines
                        if diff_mines == num_diff:
                            if self._infer_mines(list(diff_set)):
                                changed = True
                        # If mines needed in diff == 0 -> all are safe
                        elif diff_mines == 0:
                            if self._infer_safe_cells(list(diff_set)):
                                changed = True

                # Check subset relation: unknown2 < unknown1
                elif unknown2.issubset(unknown1) and unknown1 != unknown2:
                    diff_set = unknown1 - unknown2
                    diff_mines = mines_needed1 - mines_needed2
                    num_diff = len(diff_set)

                    if num_diff > 0:
                        # If mines needed in diff == size of diff -> all are mines
                        if diff_mines == num_diff:
                            if self._infer_mines(list(diff_set)):
                                changed = True
                        # If mines needed in diff == 0 -> all are safe
                        elif diff_mines == 0:
                            if self._infer_safe_cells(list(diff_set)):
                                changed = True
        return changed


    def _infer_mines(self, cells: List[Tuple[int, int]]) -> bool:
        """Mark the given cells as mines. Return True if any new cell was marked."""
        changed = False
        for r, c in cells:
            # Only infer if it's currently unknown
            if self.remaining_cells[r, c]:
                self.inferred_mine.add((r, c))
                self.remaining_cells[r, c] = False # No longer unknown
                # Update probability immediately
                self._probabilities[r,c] = 1.0
                if (r,c) in self.inferred_safe: self.inferred_safe.remove((r,c)) # Correct potential previous error
                changed = True
        return changed

    def _infer_safe_cells(self, cells: List[Tuple[int, int]]) -> bool:
        """Mark the given cells as safe. Return True if any new cell was marked."""
        changed = False
        for r, c in cells:
             # Only infer if it's currently unknown
            if self.remaining_cells[r, c]:
                self.inferred_safe.add((r, c))
                # FIXED: Mark remaining_cells as False for consistency with _infer_mines
                # Safe cells are no longer "unknown" even though they still need to be clicked
                self.remaining_cells[r, c] = False
                # Update probability immediately
                self._probabilities[r,c] = 0.0
                if (r,c) in self.inferred_mine: self.inferred_mine.remove((r,c)) # Correct potential previous error
                changed = True
        return changed

    # update_probabilities removed, integrated into predict_proba and inference methods

    def calculate_info_gain(self, i: int, j: int) -> int:
        """ Estimate information gain by number of adjacent unknown cells. """
        # Check neighbors that are currently marked as 'remaining' by the bot
        return sum(1 for (nx, ny) in self.get_neighbors(i, j) if self.remaining_cells[nx, ny])


    def best_move(self) -> Optional[Tuple[int, int]]:
        """
        Select the best move based on current probabilities.
        Returns coordinates (x, y) of the selected move.
        """
        # Check if we've already inferred any safe cells
        if self.inferred_safe:
            # Get coordinates of safe cells that haven't been tried yet
            untried_safe = [coord for coord in self.inferred_safe
                            if coord not in self.attempted_safe and not self._revealed[coord[0]][coord[1]]]
            if untried_safe:
                # Occasionally shuffle to introduce variety if multiple safe cells exist
                if len(untried_safe) > 1 and random.random() < 0.2:  # 20% chance to shuffle
                    random.shuffle(untried_safe)
                selected = untried_safe[0]
                # print(f"DEBUG: Selected safe cell {selected} with probability {self._probabilities[selected[0], selected[1]]:.4f}")
                return selected

        # If no safe cells, select the cell with the minimum probability of being a mine
        # Mask out already revealed cells and flagged cells
        mask = np.ones((self.H, self.W), dtype=bool)

        for r in range(self.H):
            for c in range(self.W):
                # Skip revealed cells and cells with flags
                if self._revealed[r][c] or (r, c) in self.inferred_mine:
                    mask[r, c] = False

        # Apply the mask: set probabilities to 1.0 (maximum) for revealed/flagged cells so they're not selected
        masked_probs = np.copy(self._probabilities)
        masked_probs[~mask] = 1.0

        # Occasionally (5% chance) prioritize information gain over minimum probability
        # This adds some diversity to the training data by making slightly riskier moves
        if random.random() < 0.05:  # 5% chance to make a slightly riskier move
            # Identify cells with reasonable probability (not too risky)
            reasonable_cells = np.where((masked_probs < 0.3) & mask)
            if len(reasonable_cells[0]) > 0:
                # Select a random cell from the reasonable set
                idx = np.random.randint(0, len(reasonable_cells[0]))
                x, y = reasonable_cells[0][idx], reasonable_cells[1][idx]
                # print(f"DEBUG: Taking slightly riskier move at ({x},{y}) with probability {self._probabilities[x, y]:.4f} for more information gain")
                return (x, y)

        # For expert mode (larger boards), occasionally make a completely random guess
        # This simulates human errors in difficult situations
        if self.H >= 16 and self.W >= 16:  # Expert mode or larger
            if random.random() < 0.15 and np.min(masked_probs[mask]) > 0.3:  # 15% chance when all remaining cells are risky
                # Get all remaining valid cells
                valid_cells = np.where(mask)
                if len(valid_cells[0]) > 0:
                    idx = np.random.randint(0, len(valid_cells[0]))
                    x, y = valid_cells[0][idx], valid_cells[1][idx]
                    # print(f"DEBUG: Making random guess at ({x},{y}) with probability {self._probabilities[x, y]:.4f} due to high uncertainty")
                    return (x, y)

        # Find the cell with minimum probability
        min_indices = np.where(masked_probs == np.min(masked_probs[mask]))
        x, y = min_indices[0][0], min_indices[1][0]  # Take the first one with min probability

        # Print debug info
        # print(f"DEBUG: Selected cell ({x},{y}) with probability {self._probabilities[x, y]:.4f}")

        return (x, y)


    def make_move(self, game: MineSweeper) -> Optional[Tuple[int, int]]:
        """
        Called by the evaluation loop. Updates inferences, probabilities, and selects best move.
        Returns (row, col) or None if no move found.
        """
        # Update our internal state from the game, but don't store the game object
        self.update_from_game(game)

        # Print debug info to verify the bot is not using hidden information
        # This will help identify if the bot is using information it shouldn't have
        # print(f"DEBUG: Bot's internal mine inference count: {len(self.inferred_mine)}")
        # print(f"DEBUG: Bot's internal safe inference count: {len(self.inferred_safe)}")

        self.repeated_inference_pass() # Run inference cycles
        # predict_proba is called at the end of repeated_inference_pass
        move = self.best_move()

        # Add verification to check if selected move hits a mine (for debugging only)
        if move is not None:
            x, y = move
            # This line is FOR DEBUGGING ONLY - indicates if bot accidentally selected a known mine
            for r, c in self.inferred_mine:
                if (r, c) == (x, y):
                    # print(f"WARNING: Bot selected a cell it believes is a mine: {move}")
                    pass

        return move


    def get_neighbors(self, row: int, col: int) -> List[Tuple[int, int]]:
        """Return valid neighboring cell coordinates."""
        neighbors = []
        for dx, dy in self.STEPS:
            nx, ny = row + dx, col + dy
            if self._is_valid_cell(nx, ny):
                neighbors.append((nx, ny))
        return neighbors

    def _is_valid_cell(self, x, y):
        """Check if coordinates are within board bounds."""
        return 0 <= x < self.H and 0 <= y < self.W

    def is_edge(self, row: int, col: int) -> bool:
        """Determine if a cell is on the edge or corner of the board."""
        return row == 0 or row == self.H - 1 or col == 0 or col == self.W - 1

    @property
    def probabilities(self) -> np.ndarray: # Return numpy array
        return self._probabilities

