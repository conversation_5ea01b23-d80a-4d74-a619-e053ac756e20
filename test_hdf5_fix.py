#!/usr/bin/env python3
"""
Simple test script to validate HDF5 context manager fix
without pydantic_settings dependency.
"""

import sys
import os
import tensorflow as tf
import numpy as np
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

def test_hdf5_loading():
    """Test HDF5 data loading with the new context manager approach"""
    print("=== Testing HDF5 Context Manager Fix ===")
    
    # Import the data loader
    try:
        from hdf5_data_loader import load_data_with_fallback
        print("✓ Successfully imported hdf5_data_loader")
    except ImportError as e:
        print(f"✗ Failed to import hdf5_data_loader: {e}")
        return False
    
    # Find a data file to test with
    data_dir = project_root / "data"
    data_files = list(data_dir.glob("*.h5"))

    if not data_files:
        # Try to find h5py test files as fallback
        h5py_test_files = list(project_root.glob("gpu_env/lib/python*/site-packages/h5py/tests/data_files/*.h5"))
        if h5py_test_files:
            print("✓ No project data files found, but we can test HDF5 context manager with h5py test file")
            # Create a simple test HDF5 file for our test
            import h5py
            import numpy as np

            test_file = project_root / "test_data.h5"
            print(f"✓ Creating test HDF5 file: {test_file}")

            # Create a simple test file that mimics our data structure
            with h5py.File(test_file, 'w') as f:
                # Create test data similar to minesweeper format
                states = np.random.rand(100, 9, 9, 12).astype(np.float32)
                moves = np.random.rand(100, 9, 9).astype(np.float32)

                f.create_dataset('states', data=states)
                f.create_dataset('moves', data=moves)
                f.attrs['total_samples'] = 100
                f.attrs['board_height'] = 9
                f.attrs['board_width'] = 9

            data_file = test_file
            print(f"✓ Created test data file: {data_file}")
        else:
            print("✗ No HDF5 data files found anywhere")
            return False
    else:
        data_file = data_files[0]
        print(f"✓ Found data file: {data_file}")
    
    # Test the new approach: manually manage data loader lifecycle
    print("\n--- Testing Manual Data Loader Management ---")
    
    try:
        # Create data loader (equivalent to load_data_with_fallback call)
        data_loader = load_data_with_fallback(str(data_file))
        if data_loader is None:
            print("✗ Failed to create data loader")
            return False
        print("✓ Created data loader")
        
        # Manually enter context (equivalent to __enter__)
        data_loader.__enter__()
        print("✓ Entered data loader context")
        
        # Get dataset info
        info = data_loader.get_dataset_info()
        print(f"✓ Dataset info: {info}")
        
        # Create TensorFlow datasets
        train_dataset, val_dataset = data_loader.create_tf_dataset(
            batch_size=32,
            validation_split=0.2,
            shuffle=True
        )
        print("✓ Created TensorFlow datasets")
        
        # Test that we can iterate over the dataset (this is where the old approach failed)
        print("\n--- Testing Dataset Iteration (Critical Test) ---")
        
        # Take a small sample to test iteration
        sample_count = 0
        for batch in train_dataset.take(2):
            states, moves = batch
            print(f"✓ Batch {sample_count + 1}: states shape {states.shape}, moves shape {moves.shape}")
            sample_count += 1
            
            # Validate data shapes
            if len(states.shape) != 4:  # Should be (batch, H, W, channels)
                print(f"✗ Unexpected states shape: {states.shape}")
                return False
            if len(moves.shape) != 2:   # Should be (batch, H*W)
                print(f"✗ Unexpected moves shape: {moves.shape}")
                return False
        
        print(f"✓ Successfully iterated over {sample_count} batches")
        
        # Test validation dataset too
        val_sample_count = 0
        for batch in val_dataset.take(1):
            states, moves = batch
            print(f"✓ Validation batch: states shape {states.shape}, moves shape {moves.shape}")
            val_sample_count += 1
        
        print(f"✓ Successfully iterated over {val_sample_count} validation batches")
        
        # Manually exit context (equivalent to __exit__)
        data_loader.__exit__(None, None, None)
        print("✓ Exited data loader context")
        
        print("\n🎉 HDF5 Context Manager Fix Test PASSED!")
        print("The data loader can now be kept alive during training without context manager issues.")
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
        # Try to cleanup
        try:
            if 'data_loader' in locals():
                data_loader.__exit__(None, None, None)
        except:
            pass
        
        return False

def test_old_approach():
    """Test the old context manager approach to show it would fail"""
    print("\n=== Testing Old Context Manager Approach (Should Fail) ===")
    
    from hdf5_data_loader import load_data_with_fallback
    
    # Find a data file
    data_dir = project_root / "data"
    data_files = list(data_dir.glob("*.h5"))
    data_file = data_files[0]
    
    try:
        # Old approach: use with statement
        with load_data_with_fallback(str(data_file)) as loader:
            train_dataset, val_dataset = loader.create_tf_dataset(
                batch_size=32,
                validation_split=0.2,
                shuffle=True
            )
            print("✓ Created datasets with old approach")
        
        # Now try to iterate (this should fail because HDF5 file is closed)
        print("--- Attempting iteration after context exit ---")
        for batch in train_dataset.take(1):
            states, moves = batch
            print(f"✗ Unexpected success: {states.shape}, {moves.shape}")
            
    except Exception as e:
        print(f"✓ Expected failure with old approach: {e}")
        return True
    
    return False

if __name__ == "__main__":
    print("HDF5 Context Manager Fix Validation")
    print("=" * 50)
    
    # Test the new approach
    success = test_hdf5_loading()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ HDF5 FIX VALIDATION SUCCESSFUL")
        print("The refactored BaseTrainer architecture should now work correctly!")
        
        # Optionally test old approach to show the difference
        print("\n" + "=" * 50)
        test_old_approach()
    else:
        print("\n" + "=" * 50)
        print("❌ HDF5 FIX VALIDATION FAILED")
        print("The HDF5 context manager issue still needs to be resolved.")
        sys.exit(1)
