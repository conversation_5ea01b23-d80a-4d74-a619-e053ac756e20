#!/usr/bin/env python3
"""
Configuration for Intermediate Difficulty Training (16x16, 40 mines)
Optimized parameters for intermediate complexity.
"""

from ..base_trainer import TrainingConfig


class IntermediateConfig(TrainingConfig):
    """Configuration for intermediate difficulty training"""
    
    # Model identification
    model_arch: str = "IntermediateCNN"
    difficulty: str = "intermediate"
    
    # Board configuration
    board_height: int = 16
    board_width: int = 16
    num_mines: int = 40
    
    # Training hyperparameters - optimized for intermediate difficulty
    batch_size: int = 256  # Reduced for larger board size
    epochs: int = 35
    learning_rate: float = 0.001
    validation_split: float = 0.15
    
    # Callback configuration
    early_stopping_patience: int = 8
    early_stopping_monitor: str = "val_move_accuracy"
    model_checkpoint_monitor: str = "val_move_accuracy"
    tensorboard_histogram_freq: int = 1
    
    # Data processing
    generator_chunk_size: int = 512  # Reduced chunk size for larger boards
    shuffle_buffer_size: int = 5000
    
    # Data file pattern for auto-detection
    data_file_pattern: str = "*intermediate*H16*W16*M40*.h5"
    
    # Directory configuration
    models_dir: str = "models/trained_simple"
    logs_dir: str = "logs/intermediate_training"
