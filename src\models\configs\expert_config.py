#!/usr/bin/env python3
"""
Configuration for Expert Difficulty Training (30x16, 99 mines)
Optimized parameters for expert complexity.
"""

from ..base_trainer import TrainingConfig


class ExpertConfig(TrainingConfig):
    """Configuration for expert difficulty training"""
    
    # Model identification
    model_arch: str = "ExpertCNN"
    difficulty: str = "expert"
    
    # Board configuration
    board_height: int = 30
    board_width: int = 16
    num_mines: int = 99
    
    # Training hyperparameters - optimized for expert difficulty
    batch_size: int = 128  # Reduced for large board size
    epochs: int = 40
    learning_rate: float = 0.001
    validation_split: float = 0.15
    
    # Callback configuration
    early_stopping_patience: int = 10  # Increased patience for expert difficulty
    early_stopping_monitor: str = "val_move_accuracy"
    model_checkpoint_monitor: str = "val_move_accuracy"
    tensorboard_histogram_freq: int = 1
    
    # Data processing
    generator_chunk_size: int = 256  # Smaller chunk size for large boards
    shuffle_buffer_size: int = 5000
    
    # Data file pattern for auto-detection
    data_file_pattern: str = "*expert*H30*W16*M99*.h5"
    
    # Directory configuration
    models_dir: str = "models/trained_simple"
    logs_dir: str = "logs/expert_training"
