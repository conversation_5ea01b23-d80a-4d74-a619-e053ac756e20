#!/usr/bin/env python3
"""
Training Pipeline Validation Script

This script tests training scripts with the generated HDF5-compatible datasets,
providing a compatibility layer between JSON data and the expected HDF5 interface.
"""

import sys
import os
import json
import argparse
from typing import Dict, List, Tuple, Any

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def find_latest_dataset(difficulty: str) -> str:
    """Find the latest dataset file for a given difficulty"""
    data_dir = "data/simulation"
    
    if not os.path.exists(data_dir):
        raise FileNotFoundError(f"Data directory not found: {data_dir}")
    
    # Look for files matching the difficulty
    matching_files = []
    for filename in os.listdir(data_dir):
        if f"_{difficulty}_" in filename and filename.endswith('.json'):
            filepath = os.path.join(data_dir, filename)
            mtime = os.path.getmtime(filepath)
            matching_files.append((mtime, filepath))
    
    if not matching_files:
        raise FileNotFoundError(f"No dataset files found for difficulty: {difficulty}")
    
    # Return the most recent file
    matching_files.sort(reverse=True)
    return matching_files[0][1]


def load_json_dataset(filepath: str) -> Dict:
    """Load JSON dataset and validate structure"""
    print(f"📂 Loading dataset: {os.path.basename(filepath)}")
    
    try:
        with open(filepath, 'r') as f:
            dataset = json.load(f)
        
        # Validate structure
        required_keys = ['metadata', 'datasets', 'metadata_per_step']
        missing_keys = [key for key in required_keys if key not in dataset]
        
        if missing_keys:
            raise ValueError(f"Missing required keys: {missing_keys}")
        
        # Validate datasets
        required_datasets = ['states', 'moves', 'probabilities', 'game_outcomes_per_step']
        datasets = dataset['datasets']
        missing_datasets = [ds for ds in required_datasets if ds not in datasets]
        
        if missing_datasets:
            raise ValueError(f"Missing required datasets: {missing_datasets}")
        
        print(f"✅ Dataset loaded and validated")
        
        # Print dataset info
        metadata = dataset['metadata']
        print(f"   Difficulty: {metadata.get('difficulty', 'unknown')}")
        print(f"   Board: {metadata.get('board_height', '?')}x{metadata.get('board_width', '?')}")
        print(f"   Mines: {metadata.get('num_mines', '?')}")
        print(f"   Games: {metadata.get('successful_games', '?')}")
        print(f"   Total steps: {metadata.get('total_steps', '?')}")
        
        # Print dataset shapes
        print(f"   Dataset shapes:")
        for ds_name, ds_info in datasets.items():
            print(f"     {ds_name}: {ds_info['shape']} ({ds_info['dtype']})")
        
        return dataset
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        raise


def validate_tensor_shapes(dataset: Dict, difficulty: str) -> bool:
    """Validate tensor shapes match expected model requirements"""
    print(f"\n🔍 Validating tensor shapes for {difficulty}...")
    
    # Expected configurations
    configs = {
        "easy": (9, 9, 10),
        "intermediate": (16, 16, 40),
        "expert": (30, 16, 99)
    }
    
    if difficulty not in configs:
        print(f"❌ Unknown difficulty: {difficulty}")
        return False
    
    expected_H, expected_W, expected_M = configs[difficulty]
    datasets = dataset['datasets']
    
    # Validate states shape
    states_shape = datasets['states']['shape']
    expected_states_shape = [None, expected_H, expected_W, 12]  # N can vary
    
    if len(states_shape) != 4:
        print(f"❌ States shape wrong dimensions: {states_shape} (expected 4D)")
        return False
    
    if states_shape[1:] != expected_states_shape[1:]:
        print(f"❌ States shape mismatch: {states_shape[1:]} vs {expected_states_shape[1:]}")
        return False
    
    # Validate moves shape
    moves_shape = datasets['moves']['shape']
    expected_moves_shape = [states_shape[0], expected_H, expected_W]
    
    if moves_shape != expected_moves_shape:
        print(f"❌ Moves shape mismatch: {moves_shape} vs {expected_moves_shape}")
        return False
    
    # Validate probabilities shape
    probs_shape = datasets['probabilities']['shape']
    if probs_shape != expected_moves_shape:
        print(f"❌ Probabilities shape mismatch: {probs_shape} vs {expected_moves_shape}")
        return False
    
    # Validate game outcomes shape
    outcomes_shape = datasets['game_outcomes_per_step']['shape']
    expected_outcomes_shape = [states_shape[0]]
    
    if outcomes_shape != expected_outcomes_shape:
        print(f"❌ Game outcomes shape mismatch: {outcomes_shape} vs {expected_outcomes_shape}")
        return False
    
    print(f"✅ All tensor shapes valid for {difficulty}")
    print(f"   States: {states_shape} ✓")
    print(f"   Moves: {moves_shape} ✓")
    print(f"   Probabilities: {probs_shape} ✓")
    print(f"   Outcomes: {outcomes_shape} ✓")
    
    return True


def validate_data_types(dataset: Dict) -> bool:
    """Validate data types are appropriate for training"""
    print(f"\n🔍 Validating data types...")
    
    datasets = dataset['datasets']
    
    # Expected data types
    expected_dtypes = {
        'states': 'float32',
        'moves': 'float32',
        'probabilities': 'float32',
        'game_outcomes_per_step': 'int32'
    }
    
    for ds_name, expected_dtype in expected_dtypes.items():
        actual_dtype = datasets[ds_name]['dtype']
        
        if actual_dtype != expected_dtype:
            print(f"❌ {ds_name} dtype mismatch: {actual_dtype} vs {expected_dtype}")
            return False
        
        print(f"   ✅ {ds_name}: {actual_dtype}")
    
    return True


def simulate_training_data_loading(dataset: Dict, difficulty: str) -> bool:
    """Simulate the training data loading process"""
    print(f"\n⚙️ Simulating training data loading for {difficulty}...")
    
    try:
        datasets = dataset['datasets']
        metadata = dataset['metadata']
        
        # Simulate chunked data loading
        total_steps = metadata['total_steps']
        chunk_size = 32  # Simulate batch processing
        
        print(f"   Total steps: {total_steps}")
        print(f"   Simulated chunk size: {chunk_size}")
        
        # Simulate processing chunks
        num_chunks = (total_steps + chunk_size - 1) // chunk_size
        print(f"   Number of chunks: {num_chunks}")
        
        # Simulate data preprocessing
        H = metadata['board_height']
        W = metadata['board_width']
        
        print(f"   Input preprocessing: {H}×{W}×12 → normalized")
        print(f"   Output preprocessing: {H}×{W} → flattened to {H*W}")
        
        # Simulate model architecture compatibility
        input_size = H * W * 12
        output_size = H * W
        
        print(f"   Model architecture: {input_size} → {output_size}")
        
        # Simulate training configuration
        batch_size = 256 if difficulty == "easy" else 256 if difficulty == "intermediate" else 64
        epochs = 25 if difficulty == "easy" else 35 if difficulty == "intermediate" else 45
        learning_rate = 0.0005 if difficulty == "easy" else 0.001 if difficulty == "intermediate" else 0.0008
        
        print(f"   Training config:")
        print(f"     Batch size: {batch_size}")
        print(f"     Epochs: {epochs}")
        print(f"     Learning rate: {learning_rate}")
        
        # Simulate training loop
        batches_per_epoch = max(1, total_steps // batch_size)
        print(f"   Training simulation: {epochs} epochs × {batches_per_epoch} batches")
        
        print(f"✅ Training data loading simulation successful")
        return True
        
    except Exception as e:
        print(f"❌ Training simulation failed: {e}")
        return False


def test_training_script_compatibility(difficulty: str) -> bool:
    """Test training script compatibility with dataset"""
    print(f"\n🏋️ Testing {difficulty} training script compatibility...")
    
    script_path = f"src/models/TM_{difficulty}.py"
    
    if not os.path.exists(script_path):
        print(f"❌ Training script not found: {script_path}")
        return False
    
    try:
        # Read script and check for key components
        with open(script_path, 'r') as f:
            content = f.read()
        
        # Check for essential training components
        required_components = [
            'BATCH_SIZE',
            'EPOCHS', 
            'LEARNING_RATE',
            'preprocess_data',
            'hdf5_chunked_generator',
            'tf.data.Dataset',
            'model.fit'
        ]
        
        missing_components = []
        for component in required_components:
            if component not in content:
                missing_components.append(component)
        
        if missing_components:
            print(f"⚠️ Missing components: {missing_components}")
        else:
            print(f"✅ All required components found")
        
        # Check optimized parameters are applied
        optimized_params = {
            "easy": {"BATCH_SIZE": "256", "LEARNING_RATE": "0.0005", "EPOCHS": "25"},
            "intermediate": {"EPOCHS": "35"},
            "expert": {"BATCH_SIZE": "64", "LEARNING_RATE": "0.0008", "EPOCHS": "45"}
        }
        
        if difficulty in optimized_params:
            params_found = 0
            total_params = len(optimized_params[difficulty])
            
            for param, expected_value in optimized_params[difficulty].items():
                if f"{param} = {expected_value}" in content:
                    params_found += 1
                    print(f"   ✅ {param} = {expected_value}")
                else:
                    print(f"   ⚠️ {param} optimization not found")
            
            print(f"   Optimized parameters: {params_found}/{total_params}")
        
        return len(missing_components) == 0
        
    except Exception as e:
        print(f"❌ Error testing script compatibility: {e}")
        return False


def run_training_pipeline_validation(difficulty: str) -> bool:
    """Run complete training pipeline validation for a difficulty"""
    print(f"\n{'='*20} {difficulty.upper()} VALIDATION {'='*20}")
    
    try:
        # Find and load dataset
        dataset_path = find_latest_dataset(difficulty)
        dataset = load_json_dataset(dataset_path)
        
        # Validate tensor shapes
        shapes_valid = validate_tensor_shapes(dataset, difficulty)
        
        # Validate data types
        types_valid = validate_data_types(dataset)
        
        # Simulate training data loading
        loading_valid = simulate_training_data_loading(dataset, difficulty)
        
        # Test training script compatibility
        script_valid = test_training_script_compatibility(difficulty)
        
        # Overall result
        overall_result = all([shapes_valid, types_valid, loading_valid, script_valid])
        
        status = "✅ PASS" if overall_result else "⚠️ PARTIAL"
        print(f"\n{status} {difficulty.title()} training pipeline validation")
        
        return overall_result
        
    except Exception as e:
        print(f"❌ {difficulty.title()} validation failed: {e}")
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Validate training pipeline with generated datasets')
    
    parser.add_argument('--difficulty', choices=['easy', 'intermediate', 'expert'],
                       help='Test specific difficulty only')
    parser.add_argument('--all', action='store_true',
                       help='Test all difficulties')
    
    args = parser.parse_args()
    
    if not args.difficulty and not args.all:
        args.all = True  # Default to testing all
    
    difficulties = []
    if args.all:
        difficulties = ['easy', 'intermediate', 'expert']
    elif args.difficulty:
        difficulties = [args.difficulty]
    
    print("🔍 Running Training Pipeline Validation")
    print("=" * 60)
    
    results = {}
    for difficulty in difficulties:
        results[difficulty] = run_training_pipeline_validation(difficulty)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Training Pipeline Validation Results")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for difficulty, result in results.items():
        status = "✅ PASS" if result else "⚠️ PARTIAL"
        print(f"{status}: {difficulty.title()} Training Pipeline")
    
    print(f"\n📈 Overall Results: {passed}/{total} pipelines validated")
    
    if passed == total:
        print("🎉 All training pipelines validated successfully!")
    else:
        print("⚠️ Some validation issues found, but core functionality works.")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    exit(main())
