import os
import time
import h5py
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models, callbacks, optimizers
import math
from model_utils import (
    create_model_filename,
    create_final_model_filename,
    save_training_summary,
    extract_bot_type_from_filename,
    extract_difficulty_from_filename,
    print_model_info
)

# ================================================================
# 0. Configuration & Paths - EXPERT DIFFICULTY (30x16, 99 mines)
# ================================================================

def find_latest_expert_data_file():
    """Automatically find the most recent expert difficulty HDF5 file."""
# Parameter Optimization Applied - Expert Model
# Optimized parameters based on difficulty analysis:
# - BATCH_SIZE: 64 (memory and gradient stability)
# - LEARNING_RATE: 0.0008 (convergence optimization)
# - EPOCHS: 45 (complexity-appropriate training time)
# - EARLY_STOPPING_PATIENCE: 10 (overfitting prevention)
# - DROPOUT_RATE: 0.3 (regularization)
# - L2_REGULARIZATION: 0.0002 (weight decay)
# Justification: Memory-efficient with extended training for complex patterns

    try:
        # Assumes script is in src/models
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    except NameError:
        # Fallback for environments where __file__ is not defined
        project_root = os.path.abspath(os.path.join(os.getcwd(), '..', '..'))
        print(f"Warning: Could not determine project root via __file__. Using: {project_root}")

    # Check both possible data directories
    possible_data_dirs = [
        os.path.join(project_root, "src", "data", "simulation"),  # New location
        os.path.join(project_root, "data", "simulation")         # Original location
    ]

    for data_dir in possible_data_dirs:
        if os.path.exists(data_dir):
            # Find all expert difficulty files
            expert_files = []
            for filename in os.listdir(data_dir):
                if filename.endswith('.h5') and 'expert' in filename.lower():
                    expert_files.append(os.path.join(data_dir, filename))

            if expert_files:
                # Return the most recent file (by modification time)
                latest_file = max(expert_files, key=os.path.getmtime)
                return latest_file, data_dir, project_root

    return None, None, project_root

# Auto-detect the data file
file_path, data_dir, project_root = find_latest_expert_data_file()

if file_path is None:
    print("❌ ERROR: No expert difficulty HDF5 files found!")
    print("📋 Please run the simulation script first to generate training data:")
    print("   cd src && python simulations.py --test  # For test data")
    print("   cd src && python simulations.py        # For full production data")
    print("\n🔍 Searched in:")
    print("   - src/data/simulation/")
    print("   - data/simulation/")
    raise FileNotFoundError("No expert difficulty training data found. Please generate data first.")

HDF5_FILENAME = os.path.basename(file_path)
models_dir = os.path.join(project_root, "models", "trained_expert")
logs_dir = os.path.join(project_root, "logs", "expert_training")

os.makedirs(models_dir, exist_ok=True)
os.makedirs(logs_dir, exist_ok=True)

print(f"✅ Auto-detected training data:")
print(f"   Project Root: {project_root}")
print(f"   Data Directory: {data_dir}")
print(f"   Data File: {HDF5_FILENAME}")
print(f"   Full Path: {file_path}")
print(f"   File Size: {os.path.getsize(file_path) / (1024*1024):.1f} MB")

# --- Training Hyperparameters ---
BATCH_SIZE = 64  # Further reduced for larger board size (30x16 = 480 cells)
EPOCHS = 45
LEARNING_RATE = 0.0008
VALIDATION_SPLIT = 0.15
SHUFFLE_BUFFER_SIZE = 5000
GENERATOR_CHUNK_SIZE = 256  # Smaller chunk size for larger boards

# --- Hardware Acceleration ---
AUTOTUNE = tf.data.AUTOTUNE

# Force GPU usage and detailed GPU configuration
print("\n=== GPU CONFIGURATION ===")
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    print(f"GPUs available: {len(gpus)}")
    for i, gpu in enumerate(gpus):
        print(f"GPU {i}: {gpu}")
        details = tf.config.experimental.get_device_details(gpu)
        print(f"  Device details: {details}")

    try:
        # Configure GPU memory growth FIRST (before any operations)
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print("✅ GPU memory growth enabled")

        # Force all operations to use GPU
        tf.config.experimental.set_visible_devices(gpus, 'GPU')
        print("✅ GPU devices set as visible")

        # Set GPU as default device for all operations
        tf.config.set_soft_device_placement(True)
        tf.config.experimental.enable_tensor_float_32_execution(False)  # For better precision
        print("✅ GPU set as default device with optimized settings")

        # Test GPU with actual computation
        with tf.device('/GPU:0'):
            test_a = tf.random.normal([1000, 1000])
            test_b = tf.random.normal([1000, 1000])
            test_c = tf.matmul(test_a, test_b)
            print(f"✅ GPU computation test successful. Result device: {test_c.device}")
            print(f"✅ GPU computation result shape: {test_c.shape}")

    except RuntimeError as e:
        print(f"❌ GPU configuration error: {e}")
        exit(1)
else:
    print("❌ No GPU detected by TensorFlow. Training will be on CPU (MUCH SLOWER).")
    exit(1)  # Exit if no GPU available

# Mixed precision setup (after GPU configuration) - Use mixed_float16 for RTX 3080 Ti
try:
    # RTX 3080 Ti supports Tensor Cores, so mixed precision should work well
    policy = tf.keras.mixed_precision.Policy('mixed_float16')
    tf.keras.mixed_precision.set_global_policy(policy)
    print("✅ Using mixed precision training (float16) - optimized for RTX 3080 Ti.")

    # Verify mixed precision is working
    print(f"✅ Compute dtype: {policy.compute_dtype}")
    print(f"✅ Variable dtype: {policy.variable_dtype}")
except Exception as e:
    print(f"⚠️ Mixed precision (float16) not available or failed: {str(e)}. Using float32.")
    # Fallback to float32
    policy = tf.keras.mixed_precision.Policy('float32')
    tf.keras.mixed_precision.set_global_policy(policy)

print("=== GPU CONFIGURATION COMPLETE ===\n")

# ================================================================
# 1. Data Loading (Memory Efficient using Chunked Generator)
# ================================================================
print("\n--- Reading Data Metadata ---")
try:
    with h5py.File(file_path, "r") as f:
        print(f"Opened HDF5 file: {HDF5_FILENAME}")
        states_shape = f["states"].shape
        moves_shape = f["moves"].shape
        N_samples, H, W, C = states_shape
        # Check moves shape:
        if len(moves_shape) == 3 and (N_samples, H, W) != moves_shape:
             raise ValueError(f"Shape mismatch between states ({states_shape}) and moves ({moves_shape})")
        elif len(moves_shape) == 2 and (N_samples, H * W) != moves_shape:
             raise ValueError(f"Shape mismatch between states ({states_shape}) and flattened moves ({moves_shape})")
        elif len(moves_shape) not in [2, 3]:
             raise ValueError(f"Unexpected moves shape: {moves_shape}")

        print(f"Total samples: {N_samples}")
        print(f"Inferred Input Shape: H={H}, W={W}, C={C}")
        states_dtype = f["states"].dtype
        moves_dtype = f["moves"].dtype
        print(f"States dtype: {states_dtype}, Moves dtype: {moves_dtype}")
        tf_states_dtype = tf.dtypes.as_dtype(states_dtype)
        if len(moves_shape) == 3:
            tf_moves_dtype = tf.dtypes.as_dtype(moves_dtype)
            moves_target_shape = (H, W)
            print(f"Moves target shape (initial): {moves_target_shape}")
        else:
            tf_moves_dtype = tf.dtypes.as_dtype(moves_dtype)
            moves_target_shape = (H * W,)
            print(f"Moves target shape (initial, flattened): {moves_target_shape}")

except Exception as e:
    print(f"ERROR reading metadata from HDF5: {e}")
    raise

def hdf5_chunked_generator(filepath, n_samples, chunk_size):
    """Generator to yield samples from HDF5 file by reading in chunks."""
    try:
        with h5py.File(filepath, 'r') as f:
            states_ds = f['states']
            moves_ds = f['moves']
            for i in range(0, n_samples, chunk_size):
                end_idx = min(i + chunk_size, n_samples)
                states_chunk = states_ds[i:end_idx]
                moves_chunk = moves_ds[i:end_idx]
                for j in range(len(states_chunk)):
                    yield (states_chunk[j], moves_chunk[j])
    except Exception as e:
        print(f"Error in HDF5 chunked generator: {e}")
        raise

# ================================================================
# 2. Data Splitting (Using tf.data)
# ================================================================
print("\n--- Creating tf.data Datasets using Chunked Generator ---")
n_val = int(N_samples * VALIDATION_SPLIT)
n_train = N_samples - n_val
print(f"Training samples: {n_train}, Validation samples: {n_val}")
print(f"Generator chunk size: {GENERATOR_CHUNK_SIZE}")

full_dataset = tf.data.Dataset.from_generator(
    lambda: hdf5_chunked_generator(file_path, N_samples, GENERATOR_CHUNK_SIZE),
    output_signature=(
        tf.TensorSpec(shape=(H, W, C), dtype=tf_states_dtype),
        tf.TensorSpec(shape=moves_target_shape, dtype=tf_moves_dtype)
    )
)

print(f"Shuffling dataset with buffer size: {SHUFFLE_BUFFER_SIZE}")
full_dataset = full_dataset.shuffle(SHUFFLE_BUFFER_SIZE, reshuffle_each_iteration=True)
train_dataset = full_dataset.take(n_train)
val_dataset = full_dataset.skip(n_train)
print("Created training and validation datasets from chunked generator.")

# ================================================================
# 3. tf.data Pipelines (Apply transformations)
# ================================================================
print("\n--- Applying Preprocessing and Batching ---")
def preprocess_data(state, move):
    """Preprocessing: Normalize state channels, cast types to float32."""
    state = tf.cast(state, tf.float32)
    if C > 11:
        mine_density_channel = state[..., 11:12]
        normalized_density = tf.clip_by_value(mine_density_channel, 0.0, 0.5) / 0.5
        other_channels = state[..., :11]
        state = tf.concat([other_channels, normalized_density], axis=-1)
    state = tf.ensure_shape(state, (H, W, C))
    return state, move

def augment_data(state, move):
    """Data Augmentation: Applies rotation and flips. Assumes move is (H, W)."""
    if len(move.shape) == 2:
        choice = tf.random.uniform(shape=[], minval=0, maxval=8, dtype=tf.int32)
        move_with_channel = tf.expand_dims(move, axis=-1)
        k = choice % 4
        if k > 0:
            state = tf.image.rot90(state, k=k)
            move_with_channel = tf.image.rot90(move_with_channel, k=k)
        if choice >= 4:
            state = tf.image.flip_left_right(state)
            move_with_channel = tf.image.flip_left_right(move_with_channel)
        move_augmented = tf.squeeze(move_with_channel, axis=-1)
        state = tf.ensure_shape(state, (H, W, C))
        move_augmented = tf.ensure_shape(move_augmented, (H, W))
        return state, move_augmented
    else:
        state = tf.ensure_shape(state, (H, W, C))
        return state, move

def flatten_and_cast_move(state, move):
    """Flattens the move tensor to (H*W,) if needed, and casts to float32."""
    state = tf.ensure_shape(state, (H, W, C))
    if len(move.shape) == 2:
        move = tf.reshape(move, [-1])
    move = tf.cast(move, tf.float32)
    move = tf.ensure_shape(move, (H * W,))
    return state, move

# Optimize data pipeline for GPU usage
print("Optimizing data pipeline for GPU...")

# Configure data pipeline with GPU optimization
train_dataset = train_dataset.map(preprocess_data, num_parallel_calls=AUTOTUNE)
if len(moves_target_shape) == 2:
    print("Applying augmentation (moves are H, W)")
    train_dataset = train_dataset.map(augment_data, num_parallel_calls=AUTOTUNE)
else:
    print("Skipping augmentation (moves are already flattened)")
train_dataset = train_dataset.map(flatten_and_cast_move, num_parallel_calls=AUTOTUNE)

# Batch and prefetch with GPU optimization
train_dataset = train_dataset.batch(BATCH_SIZE, drop_remainder=True)  # Drop remainder for consistent batch sizes
train_dataset = train_dataset.prefetch(tf.data.AUTOTUNE)  # Prefetch to GPU memory

# Apply the same optimizations to validation dataset
val_dataset = val_dataset.map(preprocess_data, num_parallel_calls=AUTOTUNE)
val_dataset = val_dataset.map(flatten_and_cast_move, num_parallel_calls=AUTOTUNE)
val_dataset = val_dataset.batch(BATCH_SIZE, drop_remainder=True)
val_dataset = val_dataset.prefetch(tf.data.AUTOTUNE)

# Explicitly move datasets to GPU device
with tf.device('/GPU:0'):
    # Create GPU-optimized datasets
    train_dataset = train_dataset.apply(tf.data.experimental.copy_to_device("/GPU:0"))
    val_dataset = val_dataset.apply(tf.data.experimental.copy_to_device("/GPU:0"))
    print("✅ Datasets moved to GPU memory")

print("Applied transformations to tf.data pipelines.")
print(f"Training dataset spec (after batching): {train_dataset.element_spec}")
print(f"Validation dataset spec (after batching): {val_dataset.element_spec}")

# ================================================================
# 4. Advanced Model Architecture for Expert Difficulty
# ================================================================
print("\n--- Building Advanced Model on GPU for Expert Difficulty ---")
input_shape_model = (H, W, C)

def create_expert_cnn_model(input_shape):
    """Creates an advanced CNN model for Minesweeper Expert - optimized for GPU."""
    h_model, w_model, _ = input_shape

    # Ensure model is created on GPU
    with tf.device('/GPU:0'):
        inputs = layers.Input(shape=input_shape, name="board_input")

        # Use mixed precision compatible layers
        x = layers.Activation('linear', dtype='float32')(inputs)

        # Advanced architecture for 30x16 boards (480 cells)
        x = layers.Conv2D(64, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(128, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(256, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)

        # First residual block
        residual1 = x
        x = layers.Conv2D(256, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(256, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.add([x, residual1])
        x = layers.Activation('relu')(x)

        # Second residual block
        residual2 = x
        x = layers.Conv2D(256, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(256, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.add([x, residual2])
        x = layers.Activation('relu')(x)

        # Third residual block for expert complexity
        residual3 = x
        x = layers.Conv2D(256, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(256, (3, 3), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.add([x, residual3])
        x = layers.Activation('relu')(x)

        # Output layers with additional processing
        x = layers.Conv2D(128, (1, 1), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(64, (1, 1), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        logits = layers.Conv2D(1, (1, 1), padding='same', name="move_logits")(x)
        flattened_logits = layers.Reshape((h_model * w_model,), name="flattened_logits")(logits)

        # Ensure output is float32 for mixed precision
        move_probs = layers.Softmax(name="move_probs", dtype='float32')(flattened_logits)

        model = models.Model(inputs=inputs, outputs=move_probs)
        return model

# Create model on GPU
with tf.device('/GPU:0'):
    model = create_expert_cnn_model(input_shape_model)
    print("✅ Model created on GPU")

model.summary()

# Verify model is on GPU
print(f"✅ Model device placement verified:")
for layer in model.layers[:3]:  # Check first few layers
    print(f"  Layer '{layer.name}': {layer.dtype_policy}")

# ================================================================
# 5. Compile Model with Improved Loss
# ================================================================
print("\n--- Compiling Model ---")

@tf.keras.utils.register_keras_serializable()
def move_accuracy(y_true, y_pred):
    """Calculates the accuracy based on the highest probability move."""
    y_true_idx = tf.argmax(y_true, axis=1)
    y_pred_idx = tf.argmax(y_pred, axis=1)
    correct = tf.equal(y_true_idx, y_pred_idx)
    return tf.reduce_mean(tf.cast(correct, tf.float32))

def top_k_move_accuracy(k=3):
    """Factory for creating top-k accuracy metric functions."""
    def top_k_acc(y_true, y_pred):
        y_true_idx = tf.argmax(y_true, axis=1, output_type=tf.int32)
        y_true_idx = tf.expand_dims(y_true_idx, axis=1)
        _, top_k_indices = tf.nn.top_k(y_pred, k=k, sorted=True)
        top_k_indices = tf.cast(top_k_indices, tf.int32)
        correct = tf.reduce_any(tf.equal(y_true_idx, top_k_indices), axis=1)
        return tf.reduce_mean(tf.cast(correct, tf.float32))
    top_k_acc.__name__ = f'top_{k}_accuracy'
    return top_k_acc

@tf.keras.utils.register_keras_serializable()
def weighted_categorical_crossentropy_loss(y_true, y_pred, label_smoothing=0.05):
    """Categorical crossentropy with optional label smoothing."""
    if label_smoothing > 0:
        n_classes = tf.cast(tf.shape(y_true)[-1], y_true.dtype)
        y_true = y_true * (1.0 - label_smoothing) + (label_smoothing / n_classes)
    y_pred = tf.clip_by_value(y_pred, tf.keras.backend.epsilon(), 1. - tf.keras.backend.epsilon())
    return tf.keras.losses.categorical_crossentropy(y_true, y_pred, from_logits=False)

initial_learning_rate = LEARNING_RATE
lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
    initial_learning_rate,
    decay_steps=1000,
    decay_rate=0.9,
    staircase=True)
optimizer = optimizers.Adam(learning_rate=lr_schedule)

# Compile model on GPU
with tf.device('/GPU:0'):
    model.compile(
        optimizer=optimizer,
        loss=weighted_categorical_crossentropy_loss,
        metrics=[
            move_accuracy,
            top_k_move_accuracy(k=3),
            top_k_move_accuracy(k=5),
            top_k_move_accuracy(k=10)  # Additional top-10 for expert difficulty
        ]
    )
    print("✅ Model compiled successfully on GPU with scheduled LR and custom functions.")

# ================================================================
# 6. Callbacks for Training
# ================================================================
print("\n--- Setting up Callbacks ---")
timestamp = time.strftime("%Y%m%d_%H%M%S")
model_filename = f'expert_cnn_H{H}W{W}_{timestamp}.keras'
early_stopping = callbacks.EarlyStopping(
    monitor='val_move_accuracy',
    patience=10,  # Increased patience for expert difficulty
    mode='max',
    restore_best_weights=True,
    verbose=1)
model_checkpoint = callbacks.ModelCheckpoint(
    filepath=os.path.join(models_dir, model_filename),
    monitor='val_move_accuracy',
    save_best_only=True,
    mode='max',
    verbose=1)
tensorboard_callback = callbacks.TensorBoard(
    log_dir=os.path.join(logs_dir, f"run_{timestamp}"),
    histogram_freq=1)
callback_list = [early_stopping, model_checkpoint, tensorboard_callback]
print("Callbacks ready (EarlyStopping, ModelCheckpoint, TensorBoard).")

# ================================================================
# 7. Train the Model
# ================================================================
print("\n--- Starting GPU Training for Expert Difficulty ---")
print(f"Batch Size: {BATCH_SIZE}")
print(f"Max Epochs: {EPOCHS}")
print(f"Optimizer: Adam with ExponentialDecay schedule, Initial LR: {initial_learning_rate}")
print(f"Input Shape: {input_shape_model}")
print(f"Output Shape (Flattened): {(H * W,)}")
print(f"Training Samples: {n_train}, Validation Samples: {n_val}")
print(f"Target Model File: {model_filename}")
print(f"Logs Dir: {os.path.join(logs_dir, f'run_{timestamp}')}")
print("\nRun `tensorboard --logdir logs/expert_training` in your project root to monitor progress.")

# Add GPU utilization monitoring
print("\n🔥 GPU TRAINING STARTING - Monitor GPU utilization in Task Manager!")
print("Expected GPU utilization: 70-95% during training")
print("If GPU utilization remains 0%, stop training with Ctrl+C")

start_train_time = time.time()
steps_per_epoch = math.ceil(n_train / BATCH_SIZE)
validation_steps = math.ceil(n_val / BATCH_SIZE)
print(f"Training steps per epoch: {steps_per_epoch}")
print(f"Validation steps per epoch: {validation_steps}")

# Force training on GPU with explicit device context
with tf.device('/GPU:0'):
    print("🚀 Starting training on GPU:0...")
    history = model.fit(
        train_dataset,
        epochs=EPOCHS,
        validation_data=val_dataset,
        steps_per_epoch=steps_per_epoch,
        validation_steps=validation_steps,
        callbacks=callback_list,
        verbose=1
    )

train_duration = time.time() - start_train_time
print(f"\n--- Training Finished ---")
print(f"Total training time: {train_duration:.2f} seconds ({train_duration / 60:.2f} minutes)")

# ================================================================
# 8. Evaluate Final Model
# ================================================================
print("\n--- Evaluating Best Model on Validation Set ---")
best_model_path = model_checkpoint.filepath
if os.path.exists(best_model_path):
    print(f"Loading best model from: {best_model_path}")
    model = tf.keras.models.load_model(
        best_model_path,
        custom_objects={
            'weighted_categorical_crossentropy_loss': weighted_categorical_crossentropy_loss,
            'move_accuracy': move_accuracy,
            'top_3_accuracy': top_k_move_accuracy(k=3),
            'top_5_accuracy': top_k_move_accuracy(k=5),
            'top_10_accuracy': top_k_move_accuracy(k=10)
        }
    )
    print("Best model loaded successfully.")
    val_results = model.evaluate(val_dataset, steps=validation_steps, verbose=1)
    print("\n--- Final Validation Results (Best Model) ---")
    for name, value in zip(model.metrics_names, val_results):
        print(f"{name}: {value:.4f}")
else:
    print(f"Warning: Best model file not found at {best_model_path}. Evaluating the model as is after the last epoch.")
    val_results = model.evaluate(val_dataset, steps=validation_steps, verbose=1)
    print("\n--- Final Validation Results (Last Epoch) ---")
    for name, value in zip(model.metrics_names, val_results):
        print(f"{name}: {value:.4f}")

# ================================================================
# 9. Save and Test Example Predictions
# ================================================================
print("\n--- Testing Model Predictions ---")
try:
    test_batch = next(iter(val_dataset.take(1)))
    test_inputs, test_targets = test_batch
    predictions = model.predict(test_inputs)
    num_examples = min(3, BATCH_SIZE, len(test_inputs))
    for i in range(num_examples):
        board = test_inputs[i].numpy()
        true_target_flat = test_targets[i].numpy()
        prediction_probs_flat = predictions[i]
        true_move_idx = np.argmax(true_target_flat)
        true_row, true_col = true_move_idx // W, true_move_idx % W
        pred_move_idx = np.argmax(prediction_probs_flat)
        pred_row, pred_col = pred_move_idx // W, pred_move_idx % W
        pred_prob = prediction_probs_flat[pred_move_idx]
        print(f"\nExample {i+1}:")
        print(f"True move: ({true_row}, {true_col}) (Index: {true_move_idx})")
        print(f"Predicted move: ({pred_row}, {pred_col}) (Index: {pred_move_idx}) with probability {pred_prob:.4f}")
        print(f"Is correct: {true_move_idx == pred_move_idx}")
except tf.errors.OutOfRangeError:
    print("Validation dataset iterator exhausted or empty batch retrieved for testing.")
except StopIteration:
    print("Validation dataset iterator finished (potentially empty dataset or take(1) failed).")
except Exception as e:
    print(f"An error occurred during prediction testing: {e}")
    import traceback
    traceback.print_exc()

print("\n--- Expert Training Script Finished ---")
