import os
import time
import h5py
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models, callbacks, optimizers
import math
import glob

# ================================================================
# 0. Configuration & Paths - UNIFIED VARIABLE BOARD SIZES (K×K for K>5)
# ================================================================
# Variable size training data files - using ALL available variable size files
VARIABLE_SIZE_FILES = [
    "minesweeper_sim_SimpleLogicBotWrapper_variable_size_5-15_VarSizeUpTo50_Density0.20_N1000_20250623_105716.h5",
    "minesweeper_sim_SimpleLogicBotWrapper_variable_size_16-30_VarSizeUpTo50_Density0.20_N1000_20250623_110911.h5",
    "minesweeper_sim_SimpleLogicBotWrapper_variable_size_31-50_VarSizeUpTo50_Density0.20_N1000_20250623_115714.h5"
]

try:
    # Assumes script is in src/models
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
except NameError:
    project_root = os.path.abspath(os.path.join(os.getcwd(), '..', '..'))
    print(f"Warning: Could not determine project root via __file__. Using: {project_root}")

data_dir = os.path.join(project_root, "data", "simulation")
models_dir = os.path.join(project_root, "models", "trained_variable_size")
logs_dir = os.path.join(project_root, "logs", "variable_size_training")

os.makedirs(models_dir, exist_ok=True)
os.makedirs(logs_dir, exist_ok=True)

print(f"Project Root: {project_root}")

# Verify all data files exist
available_files = []
for filename in VARIABLE_SIZE_FILES:
    file_path = os.path.join(data_dir, filename)
    if os.path.exists(file_path):
        available_files.append(filename)
        print(f"✅ Found: {filename}")
    else:
        print(f"❌ Missing: {filename}")

if not available_files:
    raise FileNotFoundError("No variable size data files found!")

print(f"Using {len(available_files)} variable size data files for unified training")

# --- Training Hyperparameters ---
BATCH_SIZE = 64  # Moderate batch size for variable sizes
EPOCHS = 50  # Increased epochs for complex variable size learning
LEARNING_RATE = 1e-3
VALIDATION_SPLIT = 0.15
SHUFFLE_BUFFER_SIZE = 10000  # Larger buffer for mixed data
GENERATOR_CHUNK_SIZE = 128

# Maximum board size for padding (based on data files)
MAX_BOARD_SIZE = 50  # From the variable size data

# --- Hardware Acceleration ---
AUTOTUNE = tf.data.AUTOTUNE

# Force GPU usage and detailed GPU configuration
print("\n=== GPU CONFIGURATION ===")
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    print(f"GPUs available: {len(gpus)}")
    for i, gpu in enumerate(gpus):
        print(f"GPU {i}: {gpu}")
        details = tf.config.experimental.get_device_details(gpu)
        print(f"  Device details: {details}")

    try:
        # Configure GPU memory growth FIRST (before any operations)
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print("✅ GPU memory growth enabled")

        # Force all operations to use GPU
        tf.config.experimental.set_visible_devices(gpus, 'GPU')
        print("✅ GPU devices set as visible")

        # Set GPU as default device for all operations
        tf.config.set_soft_device_placement(True)
        tf.config.experimental.enable_tensor_float_32_execution(False)  # For better precision
        print("✅ GPU set as default device with optimized settings")

        # Test GPU with actual computation
        with tf.device('/GPU:0'):
            test_a = tf.random.normal([1000, 1000])
            test_b = tf.random.normal([1000, 1000])
            test_c = tf.matmul(test_a, test_b)
            print(f"✅ GPU computation test successful. Result device: {test_c.device}")
            print(f"✅ GPU computation result shape: {test_c.shape}")

    except RuntimeError as e:
        print(f"❌ GPU configuration error: {e}")
        exit(1)
else:
    print("❌ No GPU detected by TensorFlow. Training will be on CPU (MUCH SLOWER).")
    exit(1)  # Exit if no GPU available

# Mixed precision setup (after GPU configuration) - Use mixed_float16 for RTX 3080 Ti
try:
    # RTX 3080 Ti supports Tensor Cores, so mixed precision should work well
    policy = tf.keras.mixed_precision.Policy('mixed_float16')
    tf.keras.mixed_precision.set_global_policy(policy)
    print("✅ Using mixed precision training (float16) - optimized for RTX 3080 Ti.")

    # Verify mixed precision is working
    print(f"✅ Compute dtype: {policy.compute_dtype}")
    print(f"✅ Variable dtype: {policy.variable_dtype}")
except Exception as e:
    print(f"⚠️ Mixed precision (float16) not available or failed: {str(e)}. Using float32.")
    # Fallback to float32
    policy = tf.keras.mixed_precision.Policy('float32')
    tf.keras.mixed_precision.set_global_policy(policy)

print("=== GPU CONFIGURATION COMPLETE ===\n")

# ================================================================
# 1. Multi-File Data Loading (Memory Efficient using Chunked Generator)
# ================================================================
print("\n--- Reading Data Metadata from All Files ---")

# Collect metadata from all files
all_file_info = []
total_samples = 0
max_H, max_W, max_C = 0, 0, 0

for filename in available_files:
    file_path = os.path.join(data_dir, filename)
    try:
        with h5py.File(file_path, "r") as f:
            states_shape = f["states"].shape
            moves_shape = f["moves"].shape
            N_samples, H, W, C = states_shape
            
            print(f"\n📁 {filename}:")
            print(f"  Samples: {N_samples}, Shape: H={H}, W={W}, C={C}")
            
            all_file_info.append({
                'filename': filename,
                'filepath': file_path,
                'n_samples': N_samples,
                'H': H, 'W': W, 'C': C,
                'states_shape': states_shape,
                'moves_shape': moves_shape
            })
            
            total_samples += N_samples
            max_H = max(max_H, H)
            max_W = max(max_W, W)
            max_C = max(max_C, C)
            
    except Exception as e:
        print(f"ERROR reading {filename}: {e}")
        continue

if not all_file_info:
    raise RuntimeError("No valid data files found!")

print(f"\n📊 Combined Dataset Summary:")
print(f"Total samples across all files: {total_samples}")
print(f"Maximum dimensions: H={max_H}, W={max_W}, C={max_C}")
print(f"Using unified input shape: ({max_H}, {max_W}, {max_C})")

# Use maximum dimensions for unified model
H, W, C = max_H, max_W, max_C

def multi_file_hdf5_generator(file_info_list, chunk_size):
    """Generator to yield samples from multiple HDF5 files by reading in chunks."""
    try:
        for file_info in file_info_list:
            filepath = file_info['filepath']
            n_samples = file_info['n_samples']
            file_H, file_W = file_info['H'], file_info['W']
            
            print(f"Loading data from {file_info['filename']}...")
            
            with h5py.File(filepath, 'r') as f:
                states_ds = f['states']
                moves_ds = f['moves']
                
                for i in range(0, n_samples, chunk_size):
                    end_idx = min(i + chunk_size, n_samples)
                    states_chunk = states_ds[i:end_idx]
                    moves_chunk = moves_ds[i:end_idx]
                    
                    for j in range(len(states_chunk)):
                        state = states_chunk[j]
                        move = moves_chunk[j]
                        
                        # Pad state to maximum dimensions if needed
                        if state.shape[0] < H or state.shape[1] < W:
                            padded_state = np.zeros((H, W, C), dtype=state.dtype)
                            padded_state[:state.shape[0], :state.shape[1], :state.shape[2]] = state
                            state = padded_state
                        
                        # Pad move to maximum dimensions if needed
                        if len(move.shape) == 2:  # (H, W) format
                            if move.shape[0] < H or move.shape[1] < W:
                                padded_move = np.zeros((H, W), dtype=move.dtype)
                                padded_move[:move.shape[0], :move.shape[1]] = move
                                move = padded_move
                        else:  # Flattened format
                            if move.shape[0] < H * W:
                                padded_move = np.zeros((H * W,), dtype=move.dtype)
                                padded_move[:move.shape[0]] = move
                                move = padded_move
                        
                        yield (state, move)
                        
    except Exception as e:
        print(f"Error in multi-file HDF5 generator: {e}")
        raise

# ================================================================
# 2. Data Splitting (Using tf.data)
# ================================================================
print("\n--- Creating Unified tf.data Dataset ---")
n_val = int(total_samples * VALIDATION_SPLIT)
n_train = total_samples - n_val
print(f"Training samples: {n_train}, Validation samples: {n_val}")
print(f"Generator chunk size: {GENERATOR_CHUNK_SIZE}")

# Determine output signature based on first file
first_file = all_file_info[0]
moves_target_shape = (H, W) if len(first_file['moves_shape']) == 3 else (H * W,)

full_dataset = tf.data.Dataset.from_generator(
    lambda: multi_file_hdf5_generator(all_file_info, GENERATOR_CHUNK_SIZE),
    output_signature=(
        tf.TensorSpec(shape=(H, W, C), dtype=tf.float32),
        tf.TensorSpec(shape=moves_target_shape, dtype=tf.float32)
    )
)

print(f"Shuffling unified dataset with buffer size: {SHUFFLE_BUFFER_SIZE}")
full_dataset = full_dataset.shuffle(SHUFFLE_BUFFER_SIZE, reshuffle_each_iteration=True)
train_dataset = full_dataset.take(n_train)
val_dataset = full_dataset.skip(n_train)
print("Created unified training and validation datasets from multiple files.")

# ================================================================
# 3. tf.data Pipelines (Apply transformations)
# ================================================================
print("\n--- Applying Preprocessing and Batching ---")
def preprocess_data(state, move):
    """Preprocessing: Normalize state channels, cast types to float32."""
    state = tf.cast(state, tf.float32)

    # Enhanced preprocessing for variable board sizes
    if C > 11:
        mine_density_channel = state[..., 11:12]
        normalized_density = tf.clip_by_value(mine_density_channel, 0.0, 0.5) / 0.5
        other_channels = state[..., :11]
        state = tf.concat([other_channels, normalized_density], axis=-1)

    # Ensure mask channel is properly normalized for variable sizes
    if C > 10:
        mask_channel = state[..., 10:11]  # Mask channel indicates valid board area
        mask_channel = tf.clip_by_value(mask_channel, 0.0, 1.0)
        state = tf.concat([state[..., :10], mask_channel, state[..., 11:]], axis=-1)

    state = tf.ensure_shape(state, (H, W, C))
    return state, move

def augment_data(state, move):
    """Data Augmentation: Applies rotation and flips. Assumes move is (H, W)."""
    if len(move.shape) == 2:
        choice = tf.random.uniform(shape=[], minval=0, maxval=8, dtype=tf.int32)
        move_with_channel = tf.expand_dims(move, axis=-1)
        k = choice % 4
        if k > 0:
            state = tf.image.rot90(state, k=k)
            move_with_channel = tf.image.rot90(move_with_channel, k=k)
        if choice >= 4:
            state = tf.image.flip_left_right(state)
            move_with_channel = tf.image.flip_left_right(move_with_channel)
        move_augmented = tf.squeeze(move_with_channel, axis=-1)
        state = tf.ensure_shape(state, (H, W, C))
        move_augmented = tf.ensure_shape(move_augmented, (H, W))
        return state, move_augmented
    else:
        state = tf.ensure_shape(state, (H, W, C))
        return state, move

def flatten_and_cast_move(state, move):
    """Flattens the move tensor to (H*W,) if needed, and casts to float32."""
    state = tf.ensure_shape(state, (H, W, C))
    if len(move.shape) == 2:
        move = tf.reshape(move, [-1])
    move = tf.cast(move, tf.float32)
    move = tf.ensure_shape(move, (H * W,))
    return state, move

# Optimize data pipeline for GPU usage
print("Optimizing unified data pipeline for GPU...")

# Configure data pipeline with GPU optimization
train_dataset = train_dataset.map(preprocess_data, num_parallel_calls=AUTOTUNE)
if len(moves_target_shape) == 2:
    print("Applying augmentation (moves are H, W)")
    train_dataset = train_dataset.map(augment_data, num_parallel_calls=AUTOTUNE)
else:
    print("Skipping augmentation (moves are already flattened)")
train_dataset = train_dataset.map(flatten_and_cast_move, num_parallel_calls=AUTOTUNE)

# Batch and prefetch with GPU optimization
train_dataset = train_dataset.batch(BATCH_SIZE, drop_remainder=True)
train_dataset = train_dataset.prefetch(tf.data.AUTOTUNE)

# Apply the same optimizations to validation dataset
val_dataset = val_dataset.map(preprocess_data, num_parallel_calls=AUTOTUNE)
val_dataset = val_dataset.map(flatten_and_cast_move, num_parallel_calls=AUTOTUNE)
val_dataset = val_dataset.batch(BATCH_SIZE, drop_remainder=True)
val_dataset = val_dataset.prefetch(tf.data.AUTOTUNE)

# Explicitly move datasets to GPU device
with tf.device('/GPU:0'):
    # Create GPU-optimized datasets
    train_dataset = train_dataset.apply(tf.data.experimental.copy_to_device("/GPU:0"))
    val_dataset = val_dataset.apply(tf.data.experimental.copy_to_device("/GPU:0"))
    print("✅ Unified datasets moved to GPU memory")

print("Applied transformations to unified tf.data pipelines.")
print(f"Training dataset spec (after batching): {train_dataset.element_spec}")
print(f"Validation dataset spec (after batching): {val_dataset.element_spec}")

# ================================================================
# 4. Unified Variable Size Model Architecture
# ================================================================
print("\n--- Building Unified Variable Size Model on GPU ---")
input_shape_model = (H, W, C)

def create_unified_variable_size_model(input_shape):
    """Creates a unified CNN model for ANY variable board size K×K (K>5) - optimized for GPU."""
    h_model, w_model, _ = input_shape

    # Ensure model is created on GPU
    with tf.device('/GPU:0'):
        inputs = layers.Input(shape=input_shape, name="board_input")

        # Use mixed precision compatible layers
        x = layers.Activation('linear', dtype='float32')(inputs)

        # ===== UNIFIED ARCHITECTURE FOR ANY BOARD SIZE =====
        # Extract mask for size-aware processing
        mask_input = inputs[..., 10:11] if input_shape[-1] > 10 else tf.ones_like(inputs[..., 0:1])

        # Initial feature extraction - size agnostic
        x = layers.Conv2D(64, (3, 3), padding='same', activation='relu', name='conv1')(x)
        x = layers.BatchNormalization(name='bn1')(x)
        x = layers.Conv2D(128, (3, 3), padding='same', activation='relu', name='conv2')(x)
        x = layers.BatchNormalization(name='bn2')(x)

        # Multi-scale feature extraction for different board sizes
        # Branch 1: Local patterns (3x3)
        local_branch = layers.Conv2D(128, (3, 3), padding='same', activation='relu', name='local_conv1')(x)
        local_branch = layers.BatchNormalization(name='local_bn1')(local_branch)
        local_branch = layers.Conv2D(128, (3, 3), padding='same', activation='relu', name='local_conv2')(local_branch)
        local_branch = layers.BatchNormalization(name='local_bn2')(local_branch)

        # Branch 2: Medium patterns (5x5)
        medium_branch = layers.Conv2D(128, (5, 5), padding='same', activation='relu', name='medium_conv1')(x)
        medium_branch = layers.BatchNormalization(name='medium_bn1')(medium_branch)
        medium_branch = layers.Conv2D(128, (3, 3), padding='same', activation='relu', name='medium_conv2')(medium_branch)
        medium_branch = layers.BatchNormalization(name='medium_bn2')(medium_branch)

        # Branch 3: Global patterns (7x7)
        global_branch = layers.Conv2D(128, (7, 7), padding='same', activation='relu', name='global_conv1')(x)
        global_branch = layers.BatchNormalization(name='global_bn1')(global_branch)
        global_branch = layers.Conv2D(128, (3, 3), padding='same', activation='relu', name='global_conv2')(global_branch)
        global_branch = layers.BatchNormalization(name='global_bn2')(global_branch)

        # Combine multi-scale features
        x = layers.concatenate([local_branch, medium_branch, global_branch], axis=-1, name='multi_scale_concat')
        x = layers.Conv2D(256, (1, 1), padding='same', activation='relu', name='fusion_conv')(x)
        x = layers.BatchNormalization(name='fusion_bn')(x)

        # Residual blocks for deep feature learning
        for i in range(3):
            residual = x
            x = layers.Conv2D(256, (3, 3), padding='same', activation='relu', name=f'res_conv1_{i}')(x)
            x = layers.BatchNormalization(name=f'res_bn1_{i}')(x)
            x = layers.Conv2D(256, (3, 3), padding='same', activation='relu', name=f'res_conv2_{i}')(x)
            x = layers.BatchNormalization(name=f'res_bn2_{i}')(x)
            x = layers.add([x, residual], name=f'res_add_{i}')
            x = layers.Activation('relu', name=f'res_relu_{i}')(x)

        # Size-aware attention mechanism
        # Global context with mask awareness
        masked_features = layers.multiply([x, mask_input], name='mask_features')

        # Spatial attention
        spatial_attention = layers.Conv2D(1, (1, 1), padding='same', activation='sigmoid', name='spatial_attention')(masked_features)
        x = layers.multiply([x, spatial_attention], name='spatial_attended')

        # Channel attention
        global_pool = layers.GlobalAveragePooling2D(name='global_pool')(masked_features)
        channel_attention = layers.Dense(256, activation='relu', name='channel_dense1')(global_pool)
        channel_attention = layers.Dense(256, activation='sigmoid', name='channel_dense2')(channel_attention)
        channel_attention = layers.Reshape((1, 1, 256), name='channel_reshape')(channel_attention)
        x = layers.multiply([x, channel_attention], name='channel_attended')

        # Final output layers with mask consideration
        x = layers.Conv2D(128, (1, 1), padding='same', activation='relu', name='output_conv1')(x)
        x = layers.BatchNormalization(name='output_bn1')(x)
        x = layers.Conv2D(64, (1, 1), padding='same', activation='relu', name='output_conv2')(x)
        x = layers.BatchNormalization(name='output_bn2')(x)
        logits = layers.Conv2D(1, (1, 1), padding='same', name="move_logits")(x)

        # Apply mask to logits to zero out invalid positions
        logits = layers.multiply([logits, mask_input], name="masked_logits")

        flattened_logits = layers.Reshape((h_model * w_model,), name="flattened_logits")(logits)

        # Ensure output is float32 for mixed precision
        move_probs = layers.Softmax(name="move_probs", dtype='float32')(flattened_logits)

        model = models.Model(inputs=inputs, outputs=move_probs)
        return model

# Create unified model on GPU
with tf.device('/GPU:0'):
    model = create_unified_variable_size_model(input_shape_model)
    print("✅ Unified Variable Size Model created on GPU")

model.summary()

# Verify model is on GPU
print(f"✅ Model device placement verified:")
for layer in model.layers[:3]:  # Check first few layers
    print(f"  Layer '{layer.name}': {layer.dtype_policy}")

# ================================================================
# 5. Compile Model with Size-Aware Loss
# ================================================================
print("\n--- Compiling Unified Model ---")

@tf.keras.utils.register_keras_serializable()
def move_accuracy(y_true, y_pred):
    """Calculates the accuracy based on the highest probability move."""
    y_true_idx = tf.argmax(y_true, axis=1)
    y_pred_idx = tf.argmax(y_pred, axis=1)
    correct = tf.equal(y_true_idx, y_pred_idx)
    return tf.reduce_mean(tf.cast(correct, tf.float32))

def top_k_move_accuracy(k=3):
    """Factory for creating top-k accuracy metric functions."""
    def top_k_acc(y_true, y_pred):
        y_true_idx = tf.argmax(y_true, axis=1, output_type=tf.int32)
        y_true_idx = tf.expand_dims(y_true_idx, axis=1)
        _, top_k_indices = tf.nn.top_k(y_pred, k=k, sorted=True)
        top_k_indices = tf.cast(top_k_indices, tf.int32)
        correct = tf.reduce_any(tf.equal(y_true_idx, top_k_indices), axis=1)
        return tf.reduce_mean(tf.cast(correct, tf.float32))
    top_k_acc.__name__ = f'top_{k}_accuracy'
    return top_k_acc

@tf.keras.utils.register_keras_serializable()
def size_aware_categorical_crossentropy_loss(y_true, y_pred, label_smoothing=0.05):
    """Size-aware categorical crossentropy with mask awareness and optional label smoothing."""
    if label_smoothing > 0:
        n_classes = tf.cast(tf.shape(y_true)[-1], y_true.dtype)
        y_true = y_true * (1.0 - label_smoothing) + (label_smoothing / n_classes)

    # Create mask from y_true (non-zero positions are valid)
    mask = tf.reduce_sum(y_true, axis=-1, keepdims=True)
    mask = tf.cast(mask > 0, tf.float32)

    y_pred = tf.clip_by_value(y_pred, tf.keras.backend.epsilon(), 1. - tf.keras.backend.epsilon())
    loss = tf.keras.losses.categorical_crossentropy(y_true, y_pred, from_logits=False)

    # Apply mask to loss
    masked_loss = loss * tf.squeeze(mask, axis=-1)

    # Normalize by number of valid positions per sample
    valid_positions_per_sample = tf.reduce_sum(mask, axis=1)
    normalized_loss = tf.where(
        valid_positions_per_sample > 0,
        masked_loss / tf.squeeze(valid_positions_per_sample, axis=-1),
        masked_loss
    )

    return tf.reduce_mean(normalized_loss)

initial_learning_rate = LEARNING_RATE
lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
    initial_learning_rate,
    decay_steps=500,  # Adjusted for unified variable size training
    decay_rate=0.9,
    staircase=True)
optimizer = optimizers.Adam(learning_rate=lr_schedule)

# Compile unified model on GPU
with tf.device('/GPU:0'):
    model.compile(
        optimizer=optimizer,
        loss=size_aware_categorical_crossentropy_loss,
        metrics=[
            move_accuracy,
            top_k_move_accuracy(k=3),
            top_k_move_accuracy(k=5),
            top_k_move_accuracy(k=10)
        ]
    )
    print("✅ Unified model compiled successfully on GPU with size-aware loss and custom functions.")

# ================================================================
# 6. Callbacks for Training
# ================================================================
print("\n--- Setting up Callbacks ---")
timestamp = time.strftime("%Y%m%d_%H%M%S")
model_filename = f'unified_variable_size_cnn_H{H}W{W}_{timestamp}.keras'
early_stopping = callbacks.EarlyStopping(
    monitor='val_move_accuracy',
    patience=15,  # Increased patience for complex unified learning
    mode='max',
    restore_best_weights=True,
    verbose=1)
model_checkpoint = callbacks.ModelCheckpoint(
    filepath=os.path.join(models_dir, model_filename),
    monitor='val_move_accuracy',
    save_best_only=True,
    mode='max',
    verbose=1)
reduce_lr = callbacks.ReduceLROnPlateau(
    monitor='val_loss',
    factor=0.5,
    patience=7,
    min_lr=1e-7,
    verbose=1)
tensorboard_callback = callbacks.TensorBoard(
    log_dir=os.path.join(logs_dir, f"run_{timestamp}"),
    histogram_freq=1)
callback_list = [early_stopping, model_checkpoint, reduce_lr, tensorboard_callback]
print("Callbacks ready (EarlyStopping, ModelCheckpoint, ReduceLROnPlateau, TensorBoard).")

# ================================================================
# 7. Train the Unified Model
# ================================================================
print("\n--- Starting GPU Training for Unified Variable Board Sizes (K×K for K>5) ---")
print(f"Batch Size: {BATCH_SIZE}")
print(f"Max Epochs: {EPOCHS}")
print(f"Optimizer: Adam with ExponentialDecay schedule, Initial LR: {initial_learning_rate}")
print(f"Input Shape: {input_shape_model}")
print(f"Output Shape (Flattened): {(H * W,)}")
print(f"Training Samples: {n_train}, Validation Samples: {n_val}")
print(f"Target Model File: {model_filename}")
print(f"Logs Dir: {os.path.join(logs_dir, f'run_{timestamp}')}")
print(f"Data Files Used: {len(available_files)} files covering board sizes 5-50")
print("\nRun `tensorboard --logdir logs/variable_size_training` in your project root to monitor progress.")

# Add GPU utilization monitoring
print("\n🔥 GPU TRAINING STARTING - Monitor GPU utilization in Task Manager!")
print("Expected GPU utilization: 70-95% during training")
print("If GPU utilization remains 0%, stop training with Ctrl+C")

start_train_time = time.time()
steps_per_epoch = math.ceil(n_train / BATCH_SIZE)
validation_steps = math.ceil(n_val / BATCH_SIZE)
print(f"Training steps per epoch: {steps_per_epoch}")
print(f"Validation steps per epoch: {validation_steps}")

# Force training on GPU with explicit device context
with tf.device('/GPU:0'):
    print("🚀 Starting unified variable size training on GPU:0...")
    history = model.fit(
        train_dataset,
        epochs=EPOCHS,
        validation_data=val_dataset,
        steps_per_epoch=steps_per_epoch,
        validation_steps=validation_steps,
        callbacks=callback_list,
        verbose=1
    )

train_duration = time.time() - start_train_time
print(f"\n--- Training Finished ---")
print(f"Total training time: {train_duration:.2f} seconds ({train_duration / 60:.2f} minutes)")

# ================================================================
# 8. Evaluate Final Model
# ================================================================
print("\n--- Evaluating Best Unified Model on Validation Set ---")
best_model_path = model_checkpoint.filepath
if os.path.exists(best_model_path):
    print(f"Loading best unified model from: {best_model_path}")
    model = tf.keras.models.load_model(
        best_model_path,
        custom_objects={
            'size_aware_categorical_crossentropy_loss': size_aware_categorical_crossentropy_loss,
            'move_accuracy': move_accuracy,
            'top_3_accuracy': top_k_move_accuracy(k=3),
            'top_5_accuracy': top_k_move_accuracy(k=5),
            'top_10_accuracy': top_k_move_accuracy(k=10)
        }
    )
    print("Best unified model loaded successfully.")
    val_results = model.evaluate(val_dataset, steps=validation_steps, verbose=1)
    print("\n--- Final Validation Results (Best Unified Model) ---")
    for name, value in zip(model.metrics_names, val_results):
        print(f"{name}: {value:.4f}")
else:
    print(f"Warning: Best model file not found at {best_model_path}. Evaluating the model as is after the last epoch.")
    val_results = model.evaluate(val_dataset, steps=validation_steps, verbose=1)
    print("\n--- Final Validation Results (Last Epoch) ---")
    for name, value in zip(model.metrics_names, val_results):
        print(f"{name}: {value:.4f}")

print("\n--- Unified Variable Size Training Script Finished ---")
print("🎯 This model can now handle ANY board size K×K for K>5!")
print("📊 Trained on data from board sizes 5-50 with unified architecture")
print("🔧 Use this model for Task 3: Variable Size Boards")
