#!/usr/bin/env python3
"""
Test script to validate the complete training pipeline with HDF5 context manager fix.
This bypasses pydantic_settings dependency and tests the core functionality.
"""

import sys
import os
import time
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

import tensorflow as tf
import numpy as np
from hdf5_data_loader import load_data_with_fallback

def create_simple_model(input_shape=(9, 9, 12), output_size=81):
    """Create a simple CNN model for testing"""
    model = tf.keras.Sequential([
        tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
        tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
        tf.keras.layers.Flatten(),
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.Dense(output_size, activation='softmax')
    ])
    
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def flatten_and_cast_move(state, move):
    """Flattens the move tensor to (H*W,) and casts to appropriate type."""
    H, W = 9, 9  # Easy difficulty dimensions
    
    # Handle both batched and unbatched data
    if len(state.shape) == 4:  # Batched: (batch_size, H, W, 12)
        state = tf.ensure_shape(state, (None, H, W, 12))
    else:  # Unbatched: (H, W, 12)
        state = tf.ensure_shape(state, (H, W, 12))
    
    # Flatten move tensor and convert to class indices
    if len(move.shape) == 3:  # Batched: (batch_size, H, W)
        move_flat = tf.reshape(move, (-1, H * W))
        # Convert from one-hot to class indices
        move_indices = tf.argmax(move_flat, axis=1)
    elif len(move.shape) == 2:  # Unbatched: (H, W)
        move_flat = tf.reshape(move, (-1,))
        # Convert from one-hot to class index
        move_indices = tf.argmax(move_flat)
    
    return state, move_indices

def test_complete_training_pipeline():
    """Test the complete training pipeline with HDF5 context manager fix"""
    
    print("=" * 60)
    print("COMPLETE TRAINING PIPELINE TEST")
    print("=" * 60)
    
    # Find data file
    data_file = 'src/data/simulation/minesweeper_sim_SimpleLogicBotWrapper_easy_H9_W9_M10_N8000_20250624_122828.h5'
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return False
    
    print(f"✓ Using data file: {data_file}")
    
    try:
        # Test the HDF5 context manager fix approach
        print("\n--- Step 1: Create and manage data loader ---")
        data_loader = load_data_with_fallback(data_file)
        print("✓ Created data loader")
        
        # Manually enter context (this is the fix!)
        data_loader.__enter__()
        print("✓ Entered data loader context")
        
        # Get dataset info
        info = data_loader.get_dataset_info()
        print(f"✓ Dataset info: {info['total_samples']} samples, {info['file_size_mb']:.1f} MB")
        
        # Create TensorFlow datasets
        print("\n--- Step 2: Create TensorFlow datasets ---")
        train_dataset, val_dataset = data_loader.create_tf_dataset(
            batch_size=32,
            validation_split=0.2,
            shuffle=False  # Disable shuffle to avoid indexing issues for now
        )
        print("✓ Created TensorFlow datasets")
        
        # Apply preprocessing
        train_dataset = train_dataset.map(flatten_and_cast_move, num_parallel_calls=tf.data.AUTOTUNE)
        val_dataset = val_dataset.map(flatten_and_cast_move, num_parallel_calls=tf.data.AUTOTUNE)
        print("✓ Applied preprocessing")
        
        # Create model
        print("\n--- Step 3: Create and compile model ---")
        model = create_simple_model()
        print("✓ Created model")
        print(f"✓ Model summary: {model.count_params()} parameters")
        
        # Test data loading (critical test!)
        print("\n--- Step 4: Test data loading (HDF5 context manager fix validation) ---")
        sample_batch = next(iter(train_dataset.take(1)))
        states, moves = sample_batch
        print(f"✓ Successfully loaded batch: states {states.shape}, moves {moves.shape}")
        
        # Run minimal training
        print("\n--- Step 5: Run minimal training (1 epoch) ---")
        start_time = time.time()
        
        # Limit dataset size for quick test
        train_subset = train_dataset.take(10)  # Just 10 batches for quick test
        val_subset = val_dataset.take(5)       # Just 5 batches for validation
        
        history = model.fit(
            train_subset,
            epochs=1,
            validation_data=val_subset,
            verbose=1
        )
        
        training_time = time.time() - start_time
        print(f"✓ Training completed in {training_time:.2f} seconds")
        
        # Get metrics
        final_loss = history.history['loss'][0]
        final_acc = history.history['accuracy'][0]
        val_loss = history.history['val_loss'][0]
        val_acc = history.history['val_accuracy'][0]
        
        print(f"✓ Final metrics: loss={final_loss:.4f}, acc={final_acc:.4f}")
        print(f"✓ Validation metrics: val_loss={val_loss:.4f}, val_acc={val_acc:.4f}")
        
        # Test model prediction
        print("\n--- Step 6: Test model prediction ---")
        prediction = model.predict(states[:1])
        print(f"✓ Model prediction shape: {prediction.shape}")
        print(f"✓ Prediction sample: {prediction[0][:5]}...")  # First 5 values
        
        print("\n--- Step 7: Cleanup ---")
        # Manually exit context (cleanup)
        data_loader.__exit__(None, None, None)
        print("✓ Exited data loader context")
        
        print("\n" + "=" * 60)
        print("🎉 COMPLETE TRAINING PIPELINE TEST PASSED!")
        print("✅ HDF5 context manager fix is working correctly")
        print("✅ Data loading works during training")
        print("✅ Model training completes successfully")
        print("✅ All components work together end-to-end")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
        # Cleanup on error
        try:
            data_loader.__exit__(None, None, None)
            print("✓ Cleaned up data loader on error")
        except:
            pass
            
        return False

if __name__ == "__main__":
    success = test_complete_training_pipeline()
    sys.exit(0 if success else 1)
