#!/usr/bin/env python3
"""
Unified Training Launcher for Minesweeper DL Project - Refactored
================================================================

This script provides a unified interface to launch training for any of the
Minesweeper neural network models using the new BaseTrainer architecture:

Task 1 - Traditional Boards:
- Easy (9x9, 10 mines)
- Intermediate (16x16, 40 mines)
- Expert (30x16, 99 mines)

Task 2 - Variable Mine Counts:
- Variable mines (30x30, 0-30% density)

Task 3 - Variable Board Sizes:
- Variable sizes (K×K for K>5)

Usage:
    python train_launcher.py --model easy
    python train_launcher.py --model intermediate
    python train_launcher.py --model expert
    python train_launcher.py --model variable_mines
    python train_launcher.py --model variable_size
    python train_launcher.py --model all  # Train all models sequentially

Advanced usage:
    python train_launcher.py --model easy --epochs 50 --batch_size 256
    python train_launcher.py --model variable_size --gpu_check --dry_run
    python train_launcher.py --baseline-test  # Run baseline tests for all models
"""

import os
import sys
import argparse
import subprocess
import time
import json
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import the new trainer classes and configs
from src.models.trainers.easy_trainer import EasyTrainer
from src.models.trainers.intermediate_trainer import IntermediateTrainer
from src.models.trainers.expert_trainer import ExpertTrainer
from src.models.trainers.variable_mines_trainer import VariableMinesTrainer
from src.models.trainers.variable_size_trainer import VariableSizeTrainer

from src.models.configs.easy_config import EasyConfig
from src.models.configs.intermediate_config import IntermediateConfig
from src.models.configs.expert_config import ExpertConfig
from src.models.configs.variable_mines_config import VariableMinesConfig
from src.models.configs.variable_size_config import VariableSizeConfig

# Model configurations - Updated for new architecture
MODEL_CONFIGS = {
    'easy': {
        'trainer_class': EasyTrainer,
        'config_class': EasyConfig,
        'script': 'TM_easy.py',
        'description': 'Task 1: Easy difficulty (9x9, 10 mines)',
        'estimated_time': '15-30 minutes',
        'memory_usage': 'Low',
        'recommended_batch_size': 512
    },
    'intermediate': {
        'trainer_class': IntermediateTrainer,
        'config_class': IntermediateConfig,
        'script': 'TM_intermediate.py',
        'description': 'Task 1: Intermediate difficulty (16x16, 40 mines)',
        'estimated_time': '20-40 minutes',
        'memory_usage': 'Medium',
        'recommended_batch_size': 256
    },
    'expert': {
        'trainer_class': ExpertTrainer,
        'config_class': ExpertConfig,
        'script': 'TM_expert.py',
        'description': 'Task 1: Expert difficulty (30x16, 99 mines)',
        'estimated_time': '30-60 minutes',
        'memory_usage': 'High',
        'recommended_batch_size': 128
    },
    'variable_mines': {
        'trainer_class': VariableMinesTrainer,
        'config_class': VariableMinesConfig,
        'script': 'TM_variable_mines.py',
        'description': 'Task 2: Variable mine counts (30x30, 0-30% density)',
        'estimated_time': '45-90 minutes',
        'memory_usage': 'High',
        'recommended_batch_size': 64
    },
    'variable_size': {
        'trainer_class': VariableSizeTrainer,
        'config_class': VariableSizeConfig,
        'script': 'TM_variable_size.py',
        'description': 'Task 3: Variable board sizes (K×K for K>5)',
        'estimated_time': '60-120 minutes',
        'memory_usage': 'Very High',
        'recommended_batch_size': 32
    }
}

def check_gpu_availability():
    """Check if GPU is available and properly configured."""
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ GPU Available: {len(gpus)} GPU(s) detected")
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu}")
                try:
                    details = tf.config.experimental.get_device_details(gpu)
                    print(f"   Details: {details}")
                except:
                    print(f"   Details: Unable to get device details")
            return True
        else:
            print("❌ No GPU detected by TensorFlow")
            return False
    except ImportError:
        print("❌ TensorFlow not available")
        return False
    except Exception as e:
        print(f"❌ GPU check failed: {e}")
        return False

def check_data_files(model_name):
    """Check if required data files exist for the specified model."""
    config = MODEL_CONFIGS[model_name]
    data_dir = os.path.join(project_root, "data", "simulation")
    
    missing_files = []
    
    if 'data_file' in config:
        # Single data file
        file_path = os.path.join(data_dir, config['data_file'])
        if not os.path.exists(file_path):
            missing_files.append(config['data_file'])
        else:
            print(f"✅ Data file found: {config['data_file']}")
    
    if 'data_files' in config:
        # Multiple data files
        for data_file in config['data_files']:
            file_path = os.path.join(data_dir, data_file)
            if not os.path.exists(file_path):
                missing_files.append(data_file)
            else:
                print(f"✅ Data file found: {data_file}")
    
    if missing_files:
        print(f"❌ Missing data files for {model_name}:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    return True

def check_script_exists(model_name):
    """Check if the training script exists."""
    config = MODEL_CONFIGS[model_name]
    script_path = os.path.join(project_root, "src", "models", config['script'])
    
    if os.path.exists(script_path):
        print(f"✅ Training script found: {config['script']}")
        return True
    else:
        print(f"❌ Training script missing: {config['script']}")
        return False

def display_model_info(model_name):
    """Display information about the specified model."""
    config = MODEL_CONFIGS[model_name]
    
    print(f"\n{'='*60}")
    print(f"MODEL: {model_name.upper()}")
    print(f"{'='*60}")
    print(f"Description: {config['description']}")
    print(f"Script: {config['script']}")
    print(f"Estimated Training Time: {config['estimated_time']}")
    print(f"Memory Usage: {config['memory_usage']}")
    print(f"Recommended Batch Size: {config['recommended_batch_size']}")
    
    if 'data_file' in config:
        print(f"Data File: {config['data_file']}")
    if 'data_files' in config:
        print(f"Data Files: {len(config['data_files'])} files")
        for i, file in enumerate(config['data_files'], 1):
            print(f"  {i}. {file}")
    
    print(f"{'='*60}")

def run_training_direct(model_name, args):
    """Run training directly using BaseTrainer architecture."""
    config_info = MODEL_CONFIGS[model_name]

    print(f"\n🚀 Starting direct training for {model_name}...")
    print(f"Using BaseTrainer architecture")

    # Record start time
    start_time = time.time()

    try:
        if args.dry_run:
            print(f"🔍 DRY RUN: Would create {config_info['config_class'].__name__}")
            print(f"🔍 DRY RUN: Would instantiate {config_info['trainer_class'].__name__}")
            print(f"🔍 DRY RUN: Would call trainer.train()")
            return True
        else:
            # Create configuration
            config = config_info['config_class']()

            # Override config with command line arguments
            if hasattr(args, 'epochs') and args.epochs:
                config.epochs = args.epochs
            if hasattr(args, 'batch_size') and args.batch_size:
                config.batch_size = args.batch_size
            if hasattr(args, 'learning_rate') and args.learning_rate:
                config.learning_rate = args.learning_rate

            print(f"📋 Configuration: {config.model_arch} - {config.difficulty}")
            print(f"📋 Board: {config.board_height}x{config.board_width}")
            print(f"📋 Epochs: {config.epochs}, Batch Size: {config.batch_size}")

            # Create and run trainer
            trainer = config_info['trainer_class'](config)
            history = trainer.train()

            # Record end time
            end_time = time.time()
            duration = end_time - start_time

            print(f"\n✅ Training completed successfully for {model_name}")
            print(f"⏱️ Duration: {duration/60:.1f} minutes")

            # Print final metrics
            if history and history.history:
                final_val_acc = max(history.history.get('val_move_accuracy', [0]))
                final_train_acc = history.history.get('move_accuracy', [0])[-1] if history.history.get('move_accuracy') else 0
                print(f"🎯 Final Training Accuracy: {final_train_acc:.4f}")
                print(f"🎯 Best Validation Accuracy: {final_val_acc:.4f}")

            return True

    except KeyboardInterrupt:
        print(f"\n⚠️ Training interrupted by user for {model_name}")
        return False
    except Exception as e:
        print(f"\n❌ Training failed for {model_name}: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_training(model_name, args):
    """Run training for the specified model using script execution."""
    config = MODEL_CONFIGS[model_name]
    script_path = project_root / "src" / "models" / config['script']

    print(f"\n🚀 Starting script-based training for {model_name}...")
    print(f"Script: {script_path}")
    print(f"Working directory: {project_root}")

    # Prepare command
    cmd = [sys.executable, str(script_path)]

    # Add command line arguments
    if hasattr(args, 'epochs') and args.epochs:
        cmd.extend(['--epochs', str(args.epochs)])
    if hasattr(args, 'baseline_test') and args.baseline_test:
        cmd.append('--baseline-test')

    # Set environment variables
    env = os.environ.copy()
    env['PYTHONPATH'] = str(project_root)

    # Record start time
    start_time = time.time()

    try:
        # Run the training script
        if args.dry_run:
            print(f"🔍 DRY RUN: Would execute: {' '.join(cmd)}")
            print(f"🔍 DRY RUN: Working directory: {project_root}")
            print(f"🔍 DRY RUN: Environment PYTHONPATH: {env.get('PYTHONPATH', 'Not set')}")
            return True
        else:
            print(f"▶️ Executing: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                cwd=str(project_root),
                env=env,
                capture_output=False,  # Show output in real-time
                text=True
            )

            # Record end time
            end_time = time.time()
            duration = end_time - start_time

            if result.returncode == 0:
                print(f"\n✅ Training completed successfully for {model_name}")
                print(f"⏱️ Duration: {duration/60:.1f} minutes")
                return True
            else:
                print(f"\n❌ Training failed for {model_name}")
                print(f"Exit code: {result.returncode}")
                return False

    except KeyboardInterrupt:
        print(f"\n⚠️ Training interrupted by user for {model_name}")
        return False
    except Exception as e:
        print(f"\n❌ Error running training for {model_name}: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Unified Training Launcher for Minesweeper DL Project",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        '--model', 
        choices=['easy', 'intermediate', 'expert', 'variable_mines', 'variable_size', 'all'],
        required=True,
        help='Model to train'
    )
    
    parser.add_argument(
        '--gpu_check',
        action='store_true',
        help='Check GPU availability before training'
    )
    
    parser.add_argument(
        '--dry_run',
        action='store_true', 
        help='Show what would be executed without actually running training'
    )
    
    parser.add_argument(
        '--info_only',
        action='store_true',
        help='Show model information only, do not run training'
    )
    
    parser.add_argument(
        '--list_models',
        action='store_true',
        help='List all available models and exit'
    )

    parser.add_argument(
        '--direct',
        action='store_true',
        help='Use direct BaseTrainer execution instead of script execution'
    )

    parser.add_argument(
        '--baseline-test',
        action='store_true',
        help='Run baseline test mode (5 epochs)'
    )

    parser.add_argument(
        '--epochs',
        type=int,
        help='Number of epochs to train'
    )

    parser.add_argument(
        '--batch_size',
        type=int,
        help='Batch size for training'
    )

    parser.add_argument(
        '--learning_rate',
        type=float,
        help='Learning rate for training'
    )

    args = parser.parse_args()
    
    # List models and exit
    if args.list_models:
        print("\n📋 Available Models:")
        print("="*50)
        for model_name, config in MODEL_CONFIGS.items():
            print(f"{model_name:15} - {config['description']}")
        print("="*50)
        print("all             - Train all models sequentially")
        return
    
    print(f"\n🎯 Minesweeper DL Training Launcher")
    print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Project root: {project_root}")
    
    # GPU check if requested
    if args.gpu_check:
        print(f"\n🔍 Checking GPU availability...")
        gpu_available = check_gpu_availability()
        if not gpu_available:
            print("⚠️ Warning: No GPU detected. Training will be much slower on CPU.")
            response = input("Continue anyway? (y/N): ")
            if response.lower() != 'y':
                print("Exiting...")
                return
    
    # Handle 'all' models
    if args.model == 'all':
        models_to_train = ['easy', 'intermediate', 'expert', 'variable_mines', 'variable_size']
        print(f"\n📋 Training all models sequentially: {', '.join(models_to_train)}")
    else:
        models_to_train = [args.model]
    
    # Process each model
    results = {}
    
    for model_name in models_to_train:
        display_model_info(model_name)
        
        # Show info only
        if args.info_only:
            continue
        
        # Check prerequisites
        print(f"\n🔍 Checking prerequisites for {model_name}...")
        
        if not check_script_exists(model_name):
            results[model_name] = 'SCRIPT_MISSING'
            continue
            
        if not check_data_files(model_name):
            results[model_name] = 'DATA_MISSING'
            continue
        
        print(f"✅ All prerequisites met for {model_name}")
        
        # Run training
        if not args.info_only:
            # Choose execution method
            if args.direct:
                success = run_training_direct(model_name, args)
            else:
                success = run_training(model_name, args)
            results[model_name] = 'SUCCESS' if success else 'FAILED'
    
    # Summary
    if not args.info_only and results:
        print(f"\n📊 TRAINING SUMMARY")
        print("="*50)
        for model_name, status in results.items():
            status_icon = {
                'SUCCESS': '✅',
                'FAILED': '❌', 
                'SCRIPT_MISSING': '📄❌',
                'DATA_MISSING': '💾❌'
            }.get(status, '❓')
            print(f"{model_name:15} - {status_icon} {status}")
        print("="*50)
        
        success_count = sum(1 for status in results.values() if status == 'SUCCESS')
        total_count = len(results)
        print(f"Success rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")

if __name__ == "__main__":
    main()
