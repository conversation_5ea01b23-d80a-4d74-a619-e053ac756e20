#!/usr/bin/env python3
"""
Configuration for Variable Board Sizes Training (K×K for K>5)
Optimized parameters for variable board size learning.
"""

from ..base_trainer import TrainingConfig


class VariableSizeConfig(TrainingConfig):
    """Configuration for variable board sizes training"""
    
    # Model identification
    model_arch: str = "VariableSizeCNN"
    difficulty: str = "variable_size"
    
    # Board configuration (using maximum dimensions for unified model)
    board_height: int = 50  # Maximum board size
    board_width: int = 50
    num_mines: int = 500  # Estimated for 20% density
    
    # Training hyperparameters - optimized for variable size
    batch_size: int = 32  # Very small batch size for large boards
    epochs: int = 50  # More epochs for complex variable size learning
    learning_rate: float = 0.0005  # Lower learning rate for stability
    validation_split: float = 0.15
    
    # Callback configuration
    early_stopping_patience: int = 15  # High patience for variable size complexity
    early_stopping_monitor: str = "val_move_accuracy"
    model_checkpoint_monitor: str = "val_move_accuracy"
    tensorboard_histogram_freq: int = 1
    
    # Data processing
    generator_chunk_size: int = 64  # Small chunk size for memory efficiency
    shuffle_buffer_size: int = 2000  # Reduced buffer for memory
    
    # Data file pattern for auto-detection
    data_file_pattern: str = "*variable_size*.h5"
    
    # Directory configuration
    models_dir: str = "models/trained_simple"
    logs_dir: str = "logs/variable_size_training"
