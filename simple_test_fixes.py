#!/usr/bin/env python3
"""
Simple test script to verify basic functionality of bug fixes without external dependencies.
"""

import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_minesweeper_import_and_basic_functionality():
    """Test that MineSweeper can be imported and basic functionality works"""
    print("Testing MineSweeper import and basic functionality...")
    
    try:
        from src.game.MineSweeper import MineSweeper
        
        # Test basic game creation
        game = MineSweeper(H=5, W=5, M=5)
        print(f"✅ Game created: {game.H}x{game.W} with {game.M} mines")
        
        # Test game start
        game.start()
        print("✅ Game started successfully")
        
        # Test _replace_mine error handling with impossible scenario
        try:
            # Create exclusion zone that covers entire board
            excluded_cells = {(r, c) for r in range(game.H) for c in range(game.W)}
            game._replace_mine(excluded_cells)
            print("❌ FAIL: Expected RuntimeError was not raised")
            return False
        except RuntimeError as e:
            print(f"✅ PASS: RuntimeError properly raised for impossible mine placement")
            return True
        except Exception as e:
            print(f"❌ FAIL: Unexpected error type: {type(e).__name__}: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ FAIL: Could not import MineSweeper: {e}")
        return False
    except Exception as e:
        print(f"❌ FAIL: Unexpected error: {e}")
        return False


def test_simplelogicbot_import_and_functionality():
    """Test that SimpleLogicBot can be imported and has enhanced selection method"""
    print("Testing SimpleLogicBot import and enhanced functionality...")
    
    try:
        from src.bots.SimpleLogicBot import SimpleLogicBot
        
        # Test bot creation
        bot = SimpleLogicBot(H=5, W=5, M=5)
        print("✅ SimpleLogicBot created successfully")
        
        # Test that enhanced selection method exists
        if hasattr(bot, '_select_random_with_heuristics'):
            print("✅ Enhanced selection method exists")
            
            # Test the method with some dummy data
            test_cells = [(0, 0), (1, 1), (2, 2), (4, 4)]  # Include corner and center cells
            selected = bot._select_random_with_heuristics(test_cells)
            
            if selected in test_cells:
                print(f"✅ Enhanced selection method works, selected: {selected}")
                return True
            else:
                print(f"❌ FAIL: Invalid selection: {selected}")
                return False
        else:
            print("❌ FAIL: Enhanced selection method not found")
            return False
            
    except ImportError as e:
        print(f"❌ FAIL: Could not import SimpleLogicBot: {e}")
        return False
    except Exception as e:
        print(f"❌ FAIL: Unexpected error: {e}")
        return False


def test_bayesbot_import():
    """Test that BayesBot can be imported (basic test without numpy)"""
    print("Testing BayesBot import...")
    
    try:
        from src.bots.BayesBot import BayesBot
        print("✅ BayesBot imported successfully")
        
        # Check that the methods exist
        if hasattr(BayesBot, '_infer_safe_cells') and hasattr(BayesBot, '_infer_mines'):
            print("✅ BayesBot inference methods exist")
            return True
        else:
            print("❌ FAIL: BayesBot missing expected methods")
            return False
            
    except ImportError as e:
        print(f"❌ FAIL: Could not import BayesBot: {e}")
        return False
    except Exception as e:
        print(f"❌ FAIL: Unexpected error: {e}")
        return False


def test_file_structure():
    """Test that all expected files exist"""
    print("Testing file structure...")
    
    expected_files = [
        'src/game/MineSweeper.py',
        'src/bots/SimpleLogicBot.py', 
        'src/bots/BayesBot.py',
        'src/simulations.py',
        'src/evaluate_bots.py',
        'README.md',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in expected_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ FAIL: Missing files: {missing_files}")
        return False
    else:
        print("✅ All expected files exist")
        return True


def main():
    """Run basic verification tests"""
    print("🔍 Running Basic Bug Fix Verification")
    print("=" * 40)
    
    tests = [
        test_file_structure,
        test_minesweeper_import_and_basic_functionality,
        test_simplelogicbot_import_and_functionality,
        test_bayesbot_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        print()
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ FAIL: Test {test_func.__name__} crashed: {e}")
    
    print()
    print("=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Basic verification successful!")
        return 0
    else:
        print("⚠️  Some tests failed.")
        return 1


if __name__ == "__main__":
    exit(main())
