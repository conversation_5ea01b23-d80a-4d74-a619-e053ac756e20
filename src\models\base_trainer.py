#!/usr/bin/env python3
"""
BaseTrainer: Abstract base class for Minesweeper AI training pipeline
Provides centralized training logic, GPU configuration, and type-safe configuration management.
"""

import os
import time
import glob
from abc import ABC, abstractmethod
from typing import Optional, Tuple, List, Any, Dict
from pathlib import Path

import tensorflow as tf
from pydantic_settings import BaseSettings
from pydantic import Field, validator

# Import existing utilities
from model_utils import (
    create_model_filename,
    create_final_model_filename,
    save_training_summary,
    extract_bot_type_from_filename,
    extract_difficulty_from_filename,
    print_model_info
)

# Custom metrics for Minesweeper training
@tf.keras.utils.register_keras_serializable()
def move_accuracy(y_true, y_pred):
    """Calculates the accuracy based on the highest probability move."""
    y_true_idx = tf.argmax(y_true, axis=1)
    y_pred_idx = tf.argmax(y_pred, axis=1)
    correct = tf.equal(y_true_idx, y_pred_idx)
    return tf.reduce_mean(tf.cast(correct, tf.float32))

def top_k_move_accuracy(k=3):
    """Factory for creating top-k accuracy metric functions."""
    @tf.keras.utils.register_keras_serializable()
    def top_k_acc(y_true, y_pred):
        y_true_idx = tf.argmax(y_true, axis=1, output_type=tf.int32)
        return tf.keras.metrics.sparse_top_k_categorical_accuracy(y_true_idx, y_pred, k=k)

    top_k_acc.__name__ = f'top_{k}_accuracy'
    return top_k_acc


class TrainingConfig(BaseSettings):
    """Type-safe training configuration using Pydantic BaseSettings"""
    
    # Model architecture
    model_arch: str = "SimpleCNN"
    difficulty: str = "easy"
    
    # Board dimensions (will be set by task-specific configs)
    board_height: int = 9
    board_width: int = 9
    num_mines: int = 10
    
    # Training hyperparameters
    batch_size: int = 256
    epochs: int = 30
    learning_rate: float = 0.001
    validation_split: float = 0.15
    
    # Callback configuration
    early_stopping_patience: int = 8
    early_stopping_monitor: str = "val_move_accuracy"
    model_checkpoint_monitor: str = "val_move_accuracy"
    tensorboard_histogram_freq: int = 1
    
    # File paths
    data_file: Optional[str] = None
    data_file_pattern: Optional[str] = None
    models_dir: str = "models/trained_simple"
    logs_dir: str = "logs/simple_training"
    
    # GPU settings
    enable_mixed_precision: bool = False
    gpu_memory_growth: bool = True
    
    # Data processing
    generator_chunk_size: int = 1024
    shuffle_buffer_size: int = 5000
    
    # Environment variable prefix
    class Config:
        env_prefix = "MINESWEEPER_"
        
    @validator('data_file', pre=True, always=True)
    def auto_detect_data_file(cls, v, values):
        """Auto-detect data file if not provided"""
        if v is not None:
            return v
            
        # Try to auto-detect based on pattern
        pattern = values.get('data_file_pattern')
        if pattern:
            try:
                project_root = Path(__file__).parent.parent.parent
                data_dirs = [
                    project_root / "data" / "simulation",
                    project_root / "src" / "data" / "simulation"
                ]
                
                for data_dir in data_dirs:
                    if data_dir.exists():
                        files = list(data_dir.glob(pattern))
                        if files:
                            # Return most recent file
                            latest_file = max(files, key=lambda f: f.stat().st_mtime)
                            return str(latest_file)
            except Exception:
                pass
                
        return v


class BaseTrainer(ABC):
    """Abstract base trainer implementing common training pipeline"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.model = None
        self.history = None
        self.callbacks = []
        self.train_dataset = None
        self.val_dataset = None
        self.data_loader = None  # Keep data loader alive during training
        self.project_root = self._get_project_root()

        # Ensure directories exist
        self._ensure_directories()
        
    def _get_project_root(self) -> Path:
        """Get project root directory"""
        try:
            return Path(__file__).parent.parent.parent
        except NameError:
            return Path.cwd()
    
    def _ensure_directories(self):
        """Ensure required directories exist"""
        models_dir = self.project_root / self.config.models_dir
        logs_dir = self.project_root / self.config.logs_dir
        
        models_dir.mkdir(parents=True, exist_ok=True)
        logs_dir.mkdir(parents=True, exist_ok=True)
        
    def setup_gpu(self) -> bool:
        """Centralized GPU configuration and validation"""
        print("\n=== GPU CONFIGURATION ===")
        gpus = tf.config.list_physical_devices('GPU')
        
        if not gpus:
            print("❌ No GPU detected by TensorFlow. Training will be on CPU (MUCH SLOWER).")
            return False
            
        print(f"GPUs available: {len(gpus)}")
        for i, gpu in enumerate(gpus):
            print(f"GPU {i}: {gpu}")
            try:
                details = tf.config.experimental.get_device_details(gpu)
                print(f"  Device details: {details}")
            except Exception:
                print("  Device details: Unable to get details")
                
        try:
            # Configure GPU memory growth
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, self.config.gpu_memory_growth)
            print("✅ GPU memory growth enabled")
            
            # Set soft device placement
            tf.config.set_soft_device_placement(True)
            print("✅ Soft device placement enabled")
            
            # Test GPU computation
            with tf.device('/GPU:0'):
                test_a = tf.random.normal([1000, 1000])
                test_b = tf.random.normal([1000, 1000])
                test_c = tf.matmul(test_a, test_b)
                print(f"✅ GPU computation test successful. Result device: {test_c.device}")
                
            return True
            
        except RuntimeError as e:
            print(f"❌ GPU configuration error: {e}")
            return False
    
    def create_callbacks(self) -> List[tf.keras.callbacks.Callback]:
        """Create standardized callbacks based on configuration"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # Create temporary model filename
        temp_model_filename = create_model_filename(
            model_arch=self.config.model_arch,
            difficulty=self.config.difficulty,
            board_height=self.config.board_height,
            board_width=self.config.board_width,
            bot_type=self._get_bot_type(),
            samples_count=self._get_sample_count(),
            epochs=self.config.epochs,
            batch_size=self.config.batch_size,
            learning_rate=self.config.learning_rate,
            timestamp=timestamp,
            is_temp=True
        )
        
        models_dir = self.project_root / self.config.models_dir
        logs_dir = self.project_root / self.config.logs_dir
        
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor=self.config.early_stopping_monitor,
                patience=self.config.early_stopping_patience,
                mode='max',
                restore_best_weights=True,
                verbose=1
            ),
            tf.keras.callbacks.ModelCheckpoint(
                filepath=str(models_dir / temp_model_filename),
                monitor=self.config.model_checkpoint_monitor,
                save_best_only=True,
                mode='max',
                verbose=1
            ),
            tf.keras.callbacks.TensorBoard(
                log_dir=str(logs_dir / f"run_{timestamp}"),
                histogram_freq=self.config.tensorboard_histogram_freq
            )
        ]
        
        return callbacks
    
    def _get_bot_type(self) -> str:
        """Extract bot type from data file"""
        if self.config.data_file:
            return extract_bot_type_from_filename(self.config.data_file)
        return "SimpleLogic"
    
    def _get_sample_count(self) -> int:
        """Get sample count from data file or estimate"""
        # This will be implemented when we load the data
        return 10000  # Default estimate

    def load_data(self) -> Tuple[tf.data.Dataset, tf.data.Dataset]:
        """Load and preprocess data using hdf5_data_loader"""
        if not self.config.data_file:
            raise ValueError("No data file specified and auto-detection failed")

        print(f"\n--- Loading Data ---")
        print(f"Data file: {self.config.data_file}")

        # Import the data loader
        import sys
        sys.path.append(str(self.project_root / "src"))
        from hdf5_data_loader import load_data_with_fallback

        # Load data using the existing data loader - keep it alive during training
        self.data_loader = load_data_with_fallback(self.config.data_file)
        if self.data_loader is None:
            raise ValueError(f"Failed to create data loader for {self.config.data_file}")

        # Open the data loader (equivalent to entering context manager)
        self.data_loader.__enter__()

        try:
            info = self.data_loader.get_dataset_info()
            print(f"Dataset info: {info}")

            # Create TensorFlow datasets
            train_dataset, val_dataset = self.data_loader.create_tf_dataset(
                batch_size=self.config.batch_size,
                validation_split=self.config.validation_split,
                shuffle=True
            )

            # Apply preprocessing to flatten moves from (H, W) to (H*W,)
            def flatten_and_cast_move(state, move):
                """Flattens the move tensor to (H*W,) and casts to float32."""
                H, W = self.config.board_height, self.config.board_width
                # Handle both batched and unbatched data
                if len(state.shape) == 4:  # Batched: (batch_size, H, W, 12)
                    state = tf.ensure_shape(state, (None, H, W, 12))
                else:  # Unbatched: (H, W, 12)
                    state = tf.ensure_shape(state, (H, W, 12))

                # Flatten move tensor
                if len(move.shape) == 3:  # Batched: (batch_size, H, W)
                    move = tf.reshape(move, (-1, H * W))
                elif len(move.shape) == 2:  # Unbatched: (H, W)
                    move = tf.reshape(move, (-1,))

                move = tf.cast(move, tf.float32)
                return state, move

            # Apply flattening to both datasets
            train_dataset = train_dataset.map(flatten_and_cast_move, num_parallel_calls=tf.data.AUTOTUNE)
            val_dataset = val_dataset.map(flatten_and_cast_move, num_parallel_calls=tf.data.AUTOTUNE)

            return train_dataset, val_dataset

        except Exception as e:
            # Clean up data loader if there's an error
            self._cleanup_data_loader()
            raise e

    def _cleanup_data_loader(self):
        """Clean up data loader by closing HDF5 file"""
        if self.data_loader is not None:
            try:
                self.data_loader.__exit__(None, None, None)
            except Exception as e:
                print(f"Warning: Error closing data loader: {e}")
            finally:
                self.data_loader = None

    @abstractmethod
    def create_model(self) -> tf.keras.Model:
        """Create task-specific model architecture"""
        pass

    @abstractmethod
    def get_loss_function(self):
        """Get task-specific loss function"""
        pass

    @abstractmethod
    def get_metrics(self) -> List:
        """Get task-specific metrics"""
        pass

    def compile_model(self):
        """Compile model with task-specific settings"""
        print(f"\n--- Compiling Model ---")

        # Create learning rate schedule
        lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
            self.config.learning_rate,
            decay_steps=1000,
            decay_rate=0.9,
            staircase=True
        )

        optimizer = tf.keras.optimizers.Adam(learning_rate=lr_schedule)

        # Compile on GPU
        with tf.device('/GPU:0'):
            self.model.compile(
                optimizer=optimizer,
                loss=self.get_loss_function(),
                metrics=self.get_metrics()
            )
            print("✅ Model compiled successfully on GPU.")

    def train(self) -> tf.keras.callbacks.History:
        """Execute complete training pipeline"""
        print(f"\n🚀 Starting {self.config.model_arch} training for {self.config.difficulty} difficulty...")
        print("=" * 60)

        try:
            # Setup pipeline
            gpu_available = self.setup_gpu()
            if not gpu_available:
                print("⚠️ Continuing with CPU training...")

            self.train_dataset, self.val_dataset = self.load_data()
            self.model = self.create_model()
            self.compile_model()
            self.callbacks = self.create_callbacks()

            # Print training configuration
            print(f"\n--- Training Configuration ---")
            print(f"Model Architecture: {self.config.model_arch}")
            print(f"Board Size: {self.config.board_height}x{self.config.board_width}")
            print(f"Batch Size: {self.config.batch_size}")
            print(f"Epochs: {self.config.epochs}")
            print(f"Learning Rate: {self.config.learning_rate}")
            print(f"Early Stopping Patience: {self.config.early_stopping_patience}")

            # Start training
            print(f"\n--- Starting Training ---")
            start_time = time.time()

            # Force training on GPU if available
            device = '/GPU:0' if gpu_available else '/CPU:0'
            with tf.device(device):
                print(f"🚀 Starting training on {device}...")
                self.history = self.model.fit(
                    self.train_dataset,
                    epochs=self.config.epochs,
                    validation_data=self.val_dataset,
                    callbacks=self.callbacks,
                    verbose=1
                )

            training_duration = time.time() - start_time
            print(f"\n--- Training Finished ---")
            print(f"Total training time: {training_duration:.2f} seconds ({training_duration / 60:.2f} minutes)")

            # Save final model and summary
            self.save_model_and_summary(training_duration)

            return self.history

        finally:
            # Always clean up data loader, even if training fails
            self._cleanup_data_loader()

    def save_model_and_summary(self, training_duration: float):
        """Save model with comprehensive naming and summary"""
        print(f"\n--- Saving Model and Summary ---")

        # Get best validation accuracy from history
        val_accuracy = max(self.history.history.get('val_move_accuracy', [0]))
        val_loss = min(self.history.history.get('val_loss', [float('inf')]))
        epochs_completed = len(self.history.history.get('loss', []))

        # Create final model filename with performance metrics
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        base_filename = create_model_filename(
            model_arch=self.config.model_arch,
            difficulty=self.config.difficulty,
            board_height=self.config.board_height,
            board_width=self.config.board_width,
            bot_type=self._get_bot_type(),
            samples_count=self._get_sample_count(),
            epochs=self.config.epochs,
            batch_size=self.config.batch_size,
            learning_rate=self.config.learning_rate,
            timestamp=timestamp,
            is_temp=False
        )

        final_filename = create_final_model_filename(
            base_filename=base_filename,
            val_accuracy=val_accuracy,
            val_loss=val_loss,
            epochs_completed=epochs_completed
        )

        # Save model
        models_dir = self.project_root / self.config.models_dir
        final_path = models_dir / final_filename
        self.model.save(str(final_path))

        # Create training summary
        training_config = {
            'model_arch': self.config.model_arch,
            'difficulty': self.config.difficulty,
            'board_size': f"{self.config.board_height}x{self.config.board_width}",
            'input_shape': (self.config.board_height, self.config.board_width, 12),
            'data_source': self.config.data_file,
            'bot_type': self._get_bot_type(),
            'total_samples': self._get_sample_count(),
            'epochs_completed': epochs_completed,
            'max_epochs': self.config.epochs,
            'batch_size': self.config.batch_size,
            'learning_rate': self.config.learning_rate,
            'optimizer': 'Adam with ExponentialDecay',
            'early_stopping_patience': self.config.early_stopping_patience
        }

        performance_metrics = {
            'best_val_accuracy': val_accuracy,
            'best_val_loss': val_loss,
            'final_train_accuracy': self.history.history.get('move_accuracy', [0])[-1],
            'final_train_loss': self.history.history.get('loss', [0])[-1],
            'training_duration_minutes': training_duration / 60
        }

        # Save training summary
        save_training_summary(
            model=self.model,
            history=self.history,
            model_filename=final_filename,
            models_dir=str(models_dir),
            training_config=training_config,
            performance_metrics=performance_metrics,
            training_duration=training_duration
        )

        # Print completion info
        print_model_info(final_filename, val_accuracy, str(models_dir))

        return final_filename
