#!/usr/bin/env python3
"""
Unified Bot Evaluation Script for Minesweeper AI Project

This script provides comprehensive evaluation and comparison between different bot types:
- SimpleLogicBot (rule-based logic)
- Neural Network Bot (trained models)

Supports all three project tasks:
- Task 1: Traditional boards (Easy, Intermediate, Expert)
- Task 2: Variable mine densities
- Task 3: Variable board sizes

Usage:
    python src/evaluate_bots.py --model path/to/model.h5 --difficulty easy --games 1000
    python src/evaluate_bots.py --model path/to/model.h5 --board-size 30 --mine-density 0.2 --games 500
"""

import argparse
import numpy as np
import tensorflow as tf
from tensorflow.keras import models
from tqdm import tqdm
import matplotlib.pyplot as plt
import json
import os
import sys
from collections import defaultdict
from typing import Dict, List, Tuple, Optional

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.game.MineSweeper import MineSweeper
from src.bots.SimpleLogicBot import SimpleLogicBot
from src.bots.nn_bot import NeuralNetworkBot


class MinesweeperNNAgent:
    """Wrapper for neural network bot to match evaluation interface"""
    
    def __init__(self, model_path: str, board_size: Tuple[int, int]):
        """Initialize NN agent with model and board dimensions"""
        self.model = models.load_model(model_path)
        self.H, self.W = board_size
        self.nn_bot = NeuralNetworkBot(model_path)
    
    def make_move(self, game: MineSweeper) -> Optional[Tuple[int, int]]:
        """Get next move from neural network bot"""
        try:
            state = game.get_board_state_for_nn()
            return self.nn_bot.choose_next_move(state)
        except Exception as e:
            print(f"Error in NN bot move selection: {e}")
            return None


def evaluate_bot_performance(bot_type: str, board_size: Tuple[int, int], 
                           num_mines: int, num_games: int = 1000, 
                           model_path: Optional[str] = None, 
                           batch_size: int = 100) -> Dict:
    """
    Evaluate bot performance on Minesweeper games
    
    Args:
        bot_type: "logic" or "neural"
        board_size: (height, width) tuple
        num_mines: Number of mines on board
        num_games: Number of games to evaluate
        model_path: Path to neural network model (required for neural bot)
        batch_size: Batch size for progress reporting
        
    Returns:
        Dictionary with performance statistics
    """
    H, W = board_size
    stats = {
        'wins': 0,
        'total_steps': 0,
        'total_mines_triggered': 0,
        'games_completed': 0,
        'bot_errors': 0,
        'game_details': []
    }
    
    print(f"Evaluating {bot_type} bot on {H}x{W} board with {num_mines} mines...")
    
    for batch_start in range(0, num_games, batch_size):
        current_batch = min(batch_size, num_games - batch_start)
        
        for game_idx in tqdm(range(current_batch), 
                           desc=f"{bot_type.title()} Bot (Games {batch_start+1}-{batch_start+current_batch})"):
            
            # Initialize game
            try:
                game = MineSweeper(H=H, W=W, M=num_mines)
                game.start()
            except Exception as e:
                print(f"Error initializing game: {e}")
                continue
            
            # Initialize bot
            if bot_type == "logic":
                bot = SimpleLogicBot(H, W, num_mines)
            elif bot_type == "neural":
                if model_path is None:
                    raise ValueError("Model path required for neural bot evaluation")
                bot = MinesweeperNNAgent(model_path, (H, W))
            else:
                raise ValueError(f"Unknown bot type: {bot_type}")
            
            # Game simulation
            steps = 0
            mines_triggered = 0
            won = False
            
            while not game.game_over:
                # Get bot move
                try:
                    move = bot.make_move(game)
                    if move is None:
                        break  # Bot cannot find move
                    
                    row, col = move
                    result = game.make_move(row, col)
                    steps += 1
                    
                    if result.get("mine_triggered", False):
                        mines_triggered += 1
                    
                    if result.get("won", False):
                        won = True
                        break
                        
                except Exception as e:
                    print(f"Error during game simulation: {e}")
                    stats['bot_errors'] += 1
                    break
            
            # Record game results
            stats['games_completed'] += 1
            if won:
                stats['wins'] += 1
            stats['total_steps'] += steps
            stats['total_mines_triggered'] += mines_triggered
            
            stats['game_details'].append({
                'steps': steps,
                'mines_triggered': mines_triggered,
                'won': won,
                'game_index': batch_start + game_idx
            })
    
    return stats


def compare_bots(logic_stats: Dict, neural_stats: Dict, 
                difficulty: str, save_results: bool = True) -> Dict:
    """
    Compare performance between logic and neural bots
    
    Args:
        logic_stats: Statistics from logic bot evaluation
        neural_stats: Statistics from neural bot evaluation
        difficulty: Difficulty level or description
        save_results: Whether to save detailed results to file
        
    Returns:
        Comparison results dictionary
    """
    def safe_divide(numerator, denominator):
        return numerator / denominator if denominator > 0 else 0
    
    logic_games = logic_stats['games_completed']
    neural_games = neural_stats['games_completed']
    
    comparison = {
        'difficulty': difficulty,
        'logic_bot': {
            'win_rate': safe_divide(logic_stats['wins'], logic_games),
            'avg_steps': safe_divide(logic_stats['total_steps'], logic_games),
            'avg_mines_triggered': safe_divide(logic_stats['total_mines_triggered'], logic_games),
            'games_completed': logic_games,
            'bot_errors': logic_stats['bot_errors']
        },
        'neural_bot': {
            'win_rate': safe_divide(neural_stats['wins'], neural_games),
            'avg_steps': safe_divide(neural_stats['total_steps'], neural_games),
            'avg_mines_triggered': safe_divide(neural_stats['total_mines_triggered'], neural_games),
            'games_completed': neural_games,
            'bot_errors': neural_stats['bot_errors']
        }
    }
    
    # Calculate performance differences
    comparison['performance_diff'] = {
        'win_rate_diff': comparison['neural_bot']['win_rate'] - comparison['logic_bot']['win_rate'],
        'steps_diff': comparison['neural_bot']['avg_steps'] - comparison['logic_bot']['avg_steps'],
        'mines_diff': comparison['neural_bot']['avg_mines_triggered'] - comparison['logic_bot']['avg_mines_triggered']
    }
    
    if save_results:
        filename = f"evaluation_results_{difficulty.replace(' ', '_').lower()}.json"
        with open(filename, 'w') as f:
            json.dump({
                'comparison': comparison,
                'logic_detailed': logic_stats,
                'neural_detailed': neural_stats
            }, f, indent=2)
        print(f"Detailed results saved to {filename}")
    
    return comparison


def plot_comparison(comparisons: List[Dict], save_plot: bool = True):
    """Create visualization comparing bot performances"""
    if not comparisons:
        print("No comparison data to plot")
        return
    
    difficulties = [comp['difficulty'] for comp in comparisons]
    metrics = ['win_rate', 'avg_steps', 'avg_mines_triggered']
    metric_labels = ['Win Rate', 'Average Steps', 'Average Mines Triggered']
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
        logic_values = [comp['logic_bot'][metric] for comp in comparisons]
        neural_values = [comp['neural_bot'][metric] for comp in comparisons]
        
        x = np.arange(len(difficulties))
        width = 0.35
        
        axes[i].bar(x - width/2, logic_values, width, label='Logic Bot', alpha=0.8)
        axes[i].bar(x + width/2, neural_values, width, label='Neural Bot', alpha=0.8)
        
        axes[i].set_title(label)
        axes[i].set_xlabel('Difficulty/Configuration')
        axes[i].set_ylabel(label)
        axes[i].set_xticks(x)
        axes[i].set_xticklabels(difficulties, rotation=45, ha='right')
        axes[i].legend()
        axes[i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_plot:
        plt.savefig('bot_comparison.png', dpi=300, bbox_inches='tight')
        print("Comparison plot saved as 'bot_comparison.png'")
    
    plt.show()
    return fig


def print_comparison_summary(comparison: Dict):
    """Print formatted comparison summary"""
    print(f"\n{'='*60}")
    print(f"EVALUATION RESULTS: {comparison['difficulty'].upper()}")
    print(f"{'='*60}")
    
    print(f"\nLogic Bot Performance:")
    print(f"  Win Rate: {comparison['logic_bot']['win_rate']:.4f} ({comparison['logic_bot']['win_rate']*100:.2f}%)")
    print(f"  Average Steps: {comparison['logic_bot']['avg_steps']:.2f}")
    print(f"  Average Mines Triggered: {comparison['logic_bot']['avg_mines_triggered']:.2f}")
    print(f"  Games Completed: {comparison['logic_bot']['games_completed']}")
    
    print(f"\nNeural Bot Performance:")
    print(f"  Win Rate: {comparison['neural_bot']['win_rate']:.4f} ({comparison['neural_bot']['win_rate']*100:.2f}%)")
    print(f"  Average Steps: {comparison['neural_bot']['avg_steps']:.2f}")
    print(f"  Average Mines Triggered: {comparison['neural_bot']['avg_mines_triggered']:.2f}")
    print(f"  Games Completed: {comparison['neural_bot']['games_completed']}")
    
    print(f"\nPerformance Differences (Neural - Logic):")
    print(f"  Win Rate Difference: {comparison['performance_diff']['win_rate_diff']:+.4f}")
    print(f"  Steps Difference: {comparison['performance_diff']['steps_diff']:+.2f}")
    print(f"  Mines Difference: {comparison['performance_diff']['mines_diff']:+.2f}")
    
    # Performance assessment
    if comparison['performance_diff']['win_rate_diff'] > 0:
        print(f"\n✅ Neural bot has {comparison['performance_diff']['win_rate_diff']*100:.2f}% higher win rate")
    else:
        print(f"\n❌ Neural bot has {abs(comparison['performance_diff']['win_rate_diff'])*100:.2f}% lower win rate")


def main():
    """Main evaluation function with command line interface"""
    parser = argparse.ArgumentParser(description='Evaluate and compare Minesweeper bots')

    # Model and evaluation parameters
    parser.add_argument('--model', type=str, required=True,
                       help='Path to trained neural network model (.h5 file)')
    parser.add_argument('--games', type=int, default=1000,
                       help='Number of games to evaluate (default: 1000)')
    parser.add_argument('--batch-size', type=int, default=100,
                       help='Batch size for progress reporting (default: 100)')

    # Board configuration - either predefined difficulty or custom
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--difficulty', choices=['easy', 'intermediate', 'expert'],
                      help='Predefined difficulty level')
    group.add_argument('--custom', action='store_true',
                      help='Use custom board configuration')

    # Custom board parameters (used with --custom)
    parser.add_argument('--board-size', type=int, nargs=2, metavar=('H', 'W'),
                       help='Board dimensions (height width) for custom configuration')
    parser.add_argument('--mines', type=int,
                       help='Number of mines for custom configuration')
    parser.add_argument('--mine-density', type=float,
                       help='Mine density (0.0-1.0) for custom configuration (alternative to --mines)')

    # Output options
    parser.add_argument('--no-plot', action='store_true',
                       help='Skip generating comparison plots')
    parser.add_argument('--no-save', action='store_true',
                       help='Skip saving detailed results to file')

    args = parser.parse_args()

    # Validate model file exists
    if not os.path.exists(args.model):
        print(f"Error: Model file not found: {args.model}")
        return 1

    # Determine board configuration
    if args.difficulty:
        # Predefined difficulties
        configs = {
            'easy': ((9, 9), 10),
            'intermediate': ((16, 16), 40),
            'expert': ((30, 16), 99)
        }
        board_size, num_mines = configs[args.difficulty]
        config_name = args.difficulty
    else:
        # Custom configuration
        if not args.board_size:
            print("Error: --board-size required for custom configuration")
            return 1

        board_size = tuple(args.board_size)

        if args.mines and args.mine_density:
            print("Error: Cannot specify both --mines and --mine-density")
            return 1
        elif args.mines:
            num_mines = args.mines
        elif args.mine_density:
            if not (0.0 < args.mine_density < 1.0):
                print("Error: Mine density must be between 0.0 and 1.0")
                return 1
            num_mines = int(board_size[0] * board_size[1] * args.mine_density)
        else:
            print("Error: Must specify either --mines or --mine-density for custom configuration")
            return 1

        config_name = f"custom_{board_size[0]}x{board_size[1]}_{num_mines}mines"

    print(f"Configuration: {board_size[0]}x{board_size[1]} board with {num_mines} mines")
    print(f"Evaluating {args.games} games per bot type...")

    # Evaluate both bots
    try:
        print("\n" + "="*60)
        logic_stats = evaluate_bot_performance(
            "logic", board_size, num_mines, args.games, batch_size=args.batch_size
        )

        print("\n" + "="*60)
        neural_stats = evaluate_bot_performance(
            "neural", board_size, num_mines, args.games,
            model_path=args.model, batch_size=args.batch_size
        )

        # Compare results
        comparison = compare_bots(
            logic_stats, neural_stats, config_name,
            save_results=not args.no_save
        )

        # Print summary
        print_comparison_summary(comparison)

        # Generate plot
        if not args.no_plot:
            plot_comparison([comparison], save_plot=not args.no_save)

        print(f"\nEvaluation completed successfully!")
        return 0

    except Exception as e:
        print(f"Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
