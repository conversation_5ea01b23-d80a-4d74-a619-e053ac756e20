#!/usr/bin/env python3
"""
HDF5 Data Loader for Minesweeper Deep Learning Training

This module provides memory-efficient HDF5 data loading functions optimized for
the Minesweeper training pipeline with chunked access and preprocessing.
"""

import sys
import os
import json
from typing import Generator, Tuple, Dict, Any, Optional

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Handle dependencies
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("⚠️ NumPy not available. Install with: pip install numpy")

try:
    import h5py
    HDF5_AVAILABLE = True
except ImportError:
    HDF5_AVAILABLE = False
    print("⚠️ h5py not available. Install with: pip install h5py")

try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
    print("⚠️ TensorFlow not available. Install with: pip install tensorflow")


class HDF5DataLoader:
    """Memory-efficient HDF5 data loader with chunked access"""
    
    def __init__(self, hdf5_file_path: str):
        self.file_path = hdf5_file_path
        self.file = None
        self.metadata = None
        self.board_config = None
        
    def __enter__(self):
        if not HDF5_AVAILABLE:
            raise ImportError("h5py not available. Install with: pip install h5py")
        
        self.file = h5py.File(self.file_path, 'r')
        self._load_metadata()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.file:
            self.file.close()
    
    def _load_metadata(self):
        """Load metadata from HDF5 file"""
        if 'metadata' in self.file:
            self.metadata = dict(self.file['metadata'].attrs)
            self.board_config = {
                'height': self.metadata.get('board_height', 9),
                'width': self.metadata.get('board_width', 9),
                'mines': self.metadata.get('num_mines', 10)
            }
        else:
            # Fallback for files without metadata
            states_shape = self.file['states'].shape
            self.board_config = {
                'height': states_shape[1],
                'width': states_shape[2],
                'mines': 10  # Default
            }
    
    def get_dataset_info(self) -> Dict[str, Any]:
        """Get comprehensive dataset information"""
        info = {
            'file_path': self.file_path,
            'file_size_mb': os.path.getsize(self.file_path) / (1024**2),
            'board_config': self.board_config,
            'metadata': self.metadata
        }
        
        if 'states' in self.file:
            states_dataset = self.file['states']
            info.update({
                'total_samples': states_dataset.shape[0],
                'state_shape': states_dataset.shape[1:],
                'dtype': str(states_dataset.dtype),
                'compression': states_dataset.compression,
                'chunks': states_dataset.chunks
            })
        
        return info
    
    def hdf5_chunked_generator(self, chunk_size: int = 1024, shuffle: bool = True) -> Generator[Tuple, None, None]:
        """
        Memory-efficient chunked data generator for training
        
        Args:
            chunk_size: Number of samples per chunk
            shuffle: Whether to shuffle the data
            
        Yields:
            Tuple of (states_batch, moves_batch) for training
        """
        if not HDF5_AVAILABLE or not NUMPY_AVAILABLE:
            raise ImportError("NumPy and h5py required for data loading")
        
        states_dataset = self.file['states']
        moves_dataset = self.file['moves']
        total_samples = states_dataset.shape[0]
        
        # Create indices for shuffling
        indices = np.arange(total_samples)
        if shuffle:
            np.random.shuffle(indices)
        
        # Generate chunks
        for start_idx in range(0, total_samples, chunk_size):
            end_idx = min(start_idx + chunk_size, total_samples)
            chunk_indices = indices[start_idx:end_idx]
            
            # Load chunk data
            states_chunk = states_dataset[chunk_indices]
            moves_chunk = moves_dataset[chunk_indices]
            
            yield states_chunk, moves_chunk
    
    def create_tf_dataset(self, batch_size: int = 64, validation_split: float = 0.15, 
                         shuffle: bool = True, prefetch_buffer: int = None) -> Tuple:
        """
        Create TensorFlow datasets for training and validation
        
        Args:
            batch_size: Batch size for training
            validation_split: Fraction of data for validation
            shuffle: Whether to shuffle the data
            prefetch_buffer: Prefetch buffer size (auto if None)
            
        Returns:
            Tuple of (train_dataset, val_dataset)
        """
        if not TF_AVAILABLE:
            raise ImportError("TensorFlow not available. Install with: pip install tensorflow")
        
        total_samples = self.file['states'].shape[0]
        train_size = int(total_samples * (1 - validation_split))
        
        # Create generator function for tf.data
        def data_generator():
            for states_chunk, moves_chunk in self.hdf5_chunked_generator(
                chunk_size=batch_size, shuffle=shuffle
            ):
                for i in range(len(states_chunk)):
                    yield states_chunk[i], moves_chunk[i]
        
        # Get shapes for TensorSpec
        H, W = self.board_config['height'], self.board_config['width']
        
        # Create dataset
        dataset = tf.data.Dataset.from_generator(
            data_generator,
            output_signature=(
                tf.TensorSpec(shape=(H, W, 12), dtype=tf.float32),
                tf.TensorSpec(shape=(H, W), dtype=tf.float32)
            )
        )
        
        # Split train/validation
        train_dataset = dataset.take(train_size)
        val_dataset = dataset.skip(train_size)
        
        # Batch and prefetch
        train_dataset = train_dataset.batch(batch_size)
        val_dataset = val_dataset.batch(batch_size)
        
        if prefetch_buffer is None:
            prefetch_buffer = tf.data.AUTOTUNE
        
        train_dataset = train_dataset.prefetch(prefetch_buffer)
        val_dataset = val_dataset.prefetch(prefetch_buffer)
        
        return train_dataset, val_dataset


class JSONDataLoaderCompat:
    """Compatibility loader for JSON data with HDF5-like interface"""
    
    def __init__(self, json_file_path: str):
        self.file_path = json_file_path
        self.data = None
        self.metadata = None
        self.board_config = None
        
    def __enter__(self):
        with open(self.file_path, 'r') as f:
            self.data = json.load(f)
        self._load_metadata()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
    
    def _load_metadata(self):
        """Load metadata from JSON structure"""
        if 'hdf5_metadata' in self.data:
            self.metadata = self.data['hdf5_metadata']
            board_config = self.data.get('board_configuration', {})
            self.board_config = {
                'height': board_config.get('board_height', 9),
                'width': board_config.get('board_width', 9),
                'mines': board_config.get('num_mines', 10)
            }
        elif 'metadata' in self.data:
            self.metadata = self.data['metadata']
            self.board_config = {
                'height': self.metadata.get('board_height', 9),
                'width': self.metadata.get('board_width', 9),
                'mines': self.metadata.get('num_mines', 10)
            }
    
    def get_dataset_info(self) -> Dict[str, Any]:
        """Get dataset information from JSON structure"""
        info = {
            'file_path': self.file_path,
            'file_size_mb': os.path.getsize(self.file_path) / (1024**2),
            'board_config': self.board_config,
            'metadata': self.metadata,
            'format': 'JSON (HDF5 structure demo)'
        }
        
        if 'datasets' in self.data and 'states' in self.data['datasets']:
            states_info = self.data['datasets']['states']
            info.update({
                'total_samples': states_info['shape'][0],
                'state_shape': states_info['shape'][1:],
                'dtype': states_info['dtype'],
                'compression': states_info.get('compression', 'none'),
                'chunks': states_info.get('chunks', 'none')
            })
        
        return info
    
    def hdf5_chunked_generator(self, chunk_size: int = 1024, shuffle: bool = True):
        """Mock generator for JSON compatibility testing"""
        print("⚠️ Using JSON compatibility mode - generating mock data")
        
        H, W = self.board_config['height'], self.board_config['width']
        total_samples = 100  # Mock sample count
        
        for start_idx in range(0, total_samples, chunk_size):
            end_idx = min(start_idx + chunk_size, total_samples)
            batch_size = end_idx - start_idx
            
            # Generate mock data with correct shapes
            if NUMPY_AVAILABLE:
                states_chunk = np.random.rand(batch_size, H, W, 12).astype(np.float32)
                moves_chunk = np.random.rand(batch_size, H, W).astype(np.float32)
            else:
                # Pure Python fallback
                states_chunk = [[[[0.5 for _ in range(12)] for _ in range(W)] 
                               for _ in range(H)] for _ in range(batch_size)]
                moves_chunk = [[[0.1 for _ in range(W)] for _ in range(H)] 
                              for _ in range(batch_size)]
            
            yield states_chunk, moves_chunk


def load_data_with_fallback(file_path: str):
    """Load data with automatic format detection and fallback"""
    
    if file_path.endswith('.h5') or file_path.endswith('.hdf5'):
        if HDF5_AVAILABLE:
            return HDF5DataLoader(file_path)
        else:
            print("⚠️ HDF5 file detected but h5py not available")
            return None
    
    elif file_path.endswith('.json'):
        return JSONDataLoaderCompat(file_path)
    
    else:
        print(f"⚠️ Unknown file format: {file_path}")
        return None


def test_data_loading_compatibility():
    """Test data loading compatibility with available files"""
    print("🔍 Testing Data Loading Compatibility")
    print("=" * 50)
    
    # Look for available data files
    test_dirs = ['data/hdf5_demo', 'data/simulation', 'data/test']
    found_files = []
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for filename in os.listdir(test_dir):
                if filename.endswith(('.json', '.h5', '.hdf5')):
                    found_files.append(os.path.join(test_dir, filename))
    
    if not found_files:
        print("❌ No test data files found")
        return False
    
    print(f"📁 Found {len(found_files)} data files")
    
    # Test loading each file
    for file_path in found_files[:3]:  # Test first 3 files
        print(f"\n📂 Testing: {os.path.basename(file_path)}")
        
        try:
            loader = load_data_with_fallback(file_path)
            if loader is None:
                print("  ❌ Could not create loader")
                continue
            
            with loader as data_loader:
                info = data_loader.get_dataset_info()
                print(f"  ✅ Loaded successfully")
                print(f"     Board: {info['board_config']['height']}×{info['board_config']['width']}")
                print(f"     Samples: {info.get('total_samples', 'unknown')}")
                print(f"     File size: {info['file_size_mb']:.2f} MB")
                
                # Test chunked generator
                chunk_count = 0
                for states_chunk, moves_chunk in data_loader.hdf5_chunked_generator(chunk_size=32):
                    chunk_count += 1
                    if chunk_count >= 2:  # Test first 2 chunks
                        break
                
                print(f"     Chunked loading: ✅ {chunk_count} chunks tested")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    return True


def main():
    """Main function for testing"""
    test_data_loading_compatibility()


if __name__ == "__main__":
    main()
