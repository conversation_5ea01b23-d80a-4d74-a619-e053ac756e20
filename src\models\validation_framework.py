#!/usr/bin/env python3
"""
Validation Framework for Minesweeper AI Training Pipeline
Establishes baseline metrics and validates refactored training scripts.
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import psutil
import tensorflow as tf

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class TrainingValidator:
    """Validates training scripts and measures performance metrics"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.models_dir = project_root / "src" / "models"
        self.baseline_file = self.models_dir / "baseline_metrics.json"
        self.validation_results = {}
        
    def run_baseline_test(self, script_name: str, epochs: int = 5) -> Dict:
        """Run baseline test for a training script"""
        print(f"\n🧪 Running baseline test for {script_name}")
        print("=" * 50)
        
        script_path = self.models_dir / script_name
        if not script_path.exists():
            raise FileNotFoundError(f"Training script not found: {script_path}")
        
        # Prepare command
        cmd = [
            sys.executable, str(script_path),
            "--epochs", str(epochs),
            "--baseline-test"
        ]
        
        # Monitor system resources
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Run training
        start_time = time.time()
        
        try:
            # Check if GPU is available
            gpus = tf.config.list_physical_devices('GPU')
            gpu_available = len(gpus) > 0
            
            print(f"🔧 GPU Available: {gpu_available}")
            if gpu_available:
                print(f"🔧 GPU Devices: {[gpu.name for gpu in gpus]}")
            
            # Run the training script
            result = subprocess.run(
                cmd,
                cwd=str(self.project_root),
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            elapsed_time = time.time() - start_time
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            peak_memory = final_memory  # Simplified - would need continuous monitoring
            
            # Parse output for metrics
            metrics = self._parse_training_output(result.stdout, result.stderr)
            
            # Compile results
            baseline_metrics = {
                'script_name': script_name,
                'epochs': epochs,
                'execution_time_seconds': elapsed_time,
                'execution_time_minutes': elapsed_time / 60,
                'initial_memory_mb': initial_memory,
                'peak_memory_mb': peak_memory,
                'memory_increase_mb': peak_memory - initial_memory,
                'gpu_available': gpu_available,
                'return_code': result.returncode,
                'success': result.returncode == 0,
                'stdout_lines': len(result.stdout.split('\n')),
                'stderr_lines': len(result.stderr.split('\n')),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                **metrics
            }
            
            if result.returncode == 0:
                print(f"✅ Baseline test completed successfully")
                print(f"⏱️  Execution time: {elapsed_time:.2f} seconds ({elapsed_time/60:.2f} minutes)")
                print(f"🧠 Memory usage: {peak_memory:.1f} MB (increase: {peak_memory - initial_memory:.1f} MB)")
                if 'final_accuracy' in metrics:
                    print(f"🎯 Final accuracy: {metrics['final_accuracy']:.4f}")
                if 'val_accuracy' in metrics:
                    print(f"🎯 Validation accuracy: {metrics['val_accuracy']:.4f}")
            else:
                print(f"❌ Baseline test failed with return code: {result.returncode}")
                print(f"📝 STDOUT: {result.stdout[-500:]}")  # Last 500 chars
                print(f"📝 STDERR: {result.stderr[-500:]}")  # Last 500 chars
            
            return baseline_metrics
            
        except subprocess.TimeoutExpired:
            print(f"❌ Baseline test timed out after 30 minutes")
            return {
                'script_name': script_name,
                'epochs': epochs,
                'success': False,
                'error': 'timeout',
                'execution_time_seconds': 1800,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            print(f"❌ Baseline test failed with exception: {e}")
            return {
                'script_name': script_name,
                'epochs': epochs,
                'success': False,
                'error': str(e),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def _parse_training_output(self, stdout: str, stderr: str) -> Dict:
        """Parse training output to extract metrics"""
        metrics = {}
        
        # Look for common patterns in TensorFlow training output
        lines = stdout.split('\n') + stderr.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Look for final epoch metrics
            if 'val_move_accuracy:' in line:
                try:
                    # Extract validation accuracy
                    parts = line.split('val_move_accuracy:')
                    if len(parts) > 1:
                        val_acc_str = parts[1].strip().split()[0]
                        metrics['val_accuracy'] = float(val_acc_str)
                except (ValueError, IndexError):
                    pass
            
            if 'move_accuracy:' in line and 'val_move_accuracy:' not in line:
                try:
                    # Extract training accuracy
                    parts = line.split('move_accuracy:')
                    if len(parts) > 1:
                        acc_str = parts[1].strip().split()[0]
                        metrics['final_accuracy'] = float(acc_str)
                except (ValueError, IndexError):
                    pass
            
            # Look for loss values
            if 'val_loss:' in line:
                try:
                    parts = line.split('val_loss:')
                    if len(parts) > 1:
                        loss_str = parts[1].strip().split()[0]
                        metrics['val_loss'] = float(loss_str)
                except (ValueError, IndexError):
                    pass
            
            # Look for model saving confirmation
            if 'Model saved' in line or 'Saved model' in line:
                metrics['model_saved'] = True
            
            # Look for GPU usage confirmation
            if 'GPU computation test successful' in line:
                metrics['gpu_test_passed'] = True
        
        return metrics
    
    def save_baseline_metrics(self, all_metrics: List[Dict]):
        """Save baseline metrics to JSON file"""
        baseline_data = {
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'tensorflow_version': tf.__version__,
            'python_version': sys.version,
            'platform': sys.platform,
            'metrics': all_metrics
        }
        
        with open(self.baseline_file, 'w') as f:
            json.dump(baseline_data, f, indent=2)
        
        print(f"💾 Baseline metrics saved to: {self.baseline_file}")
    
    def load_baseline_metrics(self) -> Optional[Dict]:
        """Load baseline metrics from JSON file"""
        if not self.baseline_file.exists():
            return None
        
        with open(self.baseline_file, 'r') as f:
            return json.load(f)
    
    def validate_against_baseline(self, script_name: str, current_metrics: Dict, 
                                tolerance: float = 0.02) -> Dict:
        """Validate current metrics against baseline"""
        baseline_data = self.load_baseline_metrics()
        if not baseline_data:
            return {'status': 'no_baseline', 'message': 'No baseline metrics found'}
        
        # Find baseline for this script
        baseline_metrics = None
        for metrics in baseline_data['metrics']:
            if metrics['script_name'] == script_name:
                baseline_metrics = metrics
                break
        
        if not baseline_metrics:
            return {'status': 'no_baseline_script', 'message': f'No baseline found for {script_name}'}
        
        # Compare metrics
        validation_results = {
            'status': 'pass',
            'script_name': script_name,
            'comparisons': {},
            'issues': []
        }
        
        # Compare accuracy (if available)
        if 'val_accuracy' in baseline_metrics and 'val_accuracy' in current_metrics:
            baseline_acc = baseline_metrics['val_accuracy']
            current_acc = current_metrics['val_accuracy']
            diff = abs(current_acc - baseline_acc)
            
            validation_results['comparisons']['val_accuracy'] = {
                'baseline': baseline_acc,
                'current': current_acc,
                'difference': diff,
                'within_tolerance': diff <= tolerance
            }
            
            if diff > tolerance:
                validation_results['status'] = 'fail'
                validation_results['issues'].append(
                    f"Validation accuracy difference {diff:.4f} exceeds tolerance {tolerance:.4f}"
                )
        
        # Compare execution time (allow 50% increase)
        if 'execution_time_seconds' in baseline_metrics and 'execution_time_seconds' in current_metrics:
            baseline_time = baseline_metrics['execution_time_seconds']
            current_time = current_metrics['execution_time_seconds']
            time_increase = (current_time - baseline_time) / baseline_time
            
            validation_results['comparisons']['execution_time'] = {
                'baseline_seconds': baseline_time,
                'current_seconds': current_time,
                'time_increase_percent': time_increase * 100,
                'within_tolerance': time_increase <= 0.5  # 50% increase allowed
            }
            
            if time_increase > 0.5:
                validation_results['status'] = 'warning'
                validation_results['issues'].append(
                    f"Execution time increased by {time_increase*100:.1f}% (>{50}% threshold)"
                )
        
        return validation_results


def main():
    """Main function to run baseline tests for all training scripts"""
    print("🧪 Minesweeper AI Training Validation Framework")
    print("=" * 60)
    
    validator = TrainingValidator(project_root)
    
    # List of training scripts to validate
    training_scripts = [
        "TM_easy.py",
        "TM_intermediate.py", 
        "TM_expert.py",
        "TM_variable_mines.py",
        "TM_variable_size.py"
    ]
    
    all_baseline_metrics = []
    
    for script in training_scripts:
        try:
            metrics = validator.run_baseline_test(script, epochs=5)
            all_baseline_metrics.append(metrics)
        except Exception as e:
            print(f"❌ Failed to run baseline test for {script}: {e}")
            all_baseline_metrics.append({
                'script_name': script,
                'success': False,
                'error': str(e),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            })
    
    # Save baseline metrics
    validator.save_baseline_metrics(all_baseline_metrics)
    
    # Print summary
    print(f"\n📊 Baseline Testing Summary")
    print("=" * 40)
    successful_tests = sum(1 for m in all_baseline_metrics if m.get('success', False))
    print(f"✅ Successful tests: {successful_tests}/{len(training_scripts)}")
    print(f"❌ Failed tests: {len(training_scripts) - successful_tests}/{len(training_scripts)}")
    
    for metrics in all_baseline_metrics:
        status = "✅" if metrics.get('success', False) else "❌"
        script = metrics['script_name']
        if metrics.get('success', False):
            time_min = metrics.get('execution_time_minutes', 0)
            acc = metrics.get('val_accuracy', 0)
            print(f"{status} {script}: {time_min:.1f}min, val_acc={acc:.4f}")
        else:
            error = metrics.get('error', 'unknown')
            print(f"{status} {script}: {error}")


if __name__ == "__main__":
    main()
