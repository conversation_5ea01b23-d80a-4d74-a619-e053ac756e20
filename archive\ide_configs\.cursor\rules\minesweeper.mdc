---
description: Guidelines for developing a neural network-based Minesweeper bot as specified in the final project requirements.
globs: **/*.py, **/minesweeper*.*, **/*neural*.*
alwaysApply: false
---

- **Project Overview:**
  - Develop neural network-based bots to play Minesweeper
  - Compare performance against a traditional logic bot
  - Complete three main tasks with increasing complexity
  - Focus on network learning rather than hardcoding game logic

- **Core Requirements:**
  - **Neural Network Focus**
    - The agent should implement no reasoning outside the neural network
    - Goal is for the network to learn Minesweeper strategy, not to code it
    - Inputs should represent only information available to the player
  
  - **Input Representation**
    - Represent revealed cells with their clue numbers (0-8)
    - Represent unrevealed cells with a distinct encoding
    - Represent flagged mines (if applicable) with a distinct encoding
    - Consider using one-hot encoding for cell states
  
  - **Output Representation**
    - Network should output values for each cell to determine next move
    - Options include:
      - Probability that a cell contains a mine
      - Value/reward of revealing each cell
      - Directly predicting which cell to open next
  
  - **Model Architectures**
    - **Convolutional Networks**
      - Use 2D convolutions to capture spatial patterns on the board
      - Consider kernels of varying sizes to capture different pattern scales
      - Apply appropriate padding to handle board edges
    
    - **Sequential Models**
      - Consider representing game history for sequential decision making
      - Use RNNs or Transformers to process the sequence of game states
      - Experiment with attention mechanisms to focus on relevant board areas
    
    - **Variable Size Handling**
      - Use fully convolutional networks for variable board sizes
      - Consider patch-based approaches for larger boards
      - Implement attention mechanisms that can handle variable-sized inputs

- **Task-Specific Guidelines:**
  - **Task 1: Traditional Boards**
    ```python
    # Board dimensions by difficulty
    EASY = {"height": 9, "width": 9, "mines": 10}
    INTERMEDIATE = {"height": 16, "width": 16, "mines": 40}
    EXPERT = {"height": 30, "width": 16, "mines": 99}
    ```
    - Train separate networks for each difficulty or a single adaptive network
    - Compare with the logic bot on:
      - Board clearing success rate
      - Average steps survived
      - Mines triggered when allowed to continue
  
  - **Task 2: Variable Mine Count**
    - Board size: 30x30
    - Mine density: 0% to 30% of cells
    - Network must adapt to different mine densities
    - Consider including mine count or density as input feature
    - Plot performance as a function of mine density
  
  - **Task 3: Variable Board Size**
    - Support arbitrary square boards (KxK) for K > 5
    - Design architecture that handles variable-sized inputs
    - Use a single network for all board sizes
    - Plot performance as a function of board size K

- **Data Generation:**
  ```python
  # Example data generation approach
  def generate_training_data(num_samples=10000):
      training_data = []
      for _ in range(num_samples):
          # Initialize a random game
          game = MinesweeperGame(width=16, height=16, num_mines=40)
          
          # Play game using logic bot or random moves to generate states
          while not game.is_game_over():
              # Get current state
              state = game.get_visible_state()
              
              # Choose best move using logic bot or other strategy
              best_move = logic_bot.get_next_move(game)
              
              # Save state and target
              training_data.append((state, best_move))
              
              # Make the move
              game.reveal_cell(best_move)
      
      return training_data
  ```

- **Evaluation Metrics:**
  - **Primary Metrics**
    - Success rate: % of games where all safe cells are cleared
    - Survival steps: Average number of steps before hitting a mine
    - Mine efficiency: Average number of mines triggered to clear all safe cells
  
  - **Comparative Analysis**
    - Plot metrics against logic bot performance
    - Identify scenarios where neural bot outperforms logic bot
    - Analyze decision differences in specific board configurations

- **Training Strategies:**
  - **Curriculum Learning**
    - Start with simple, small boards
    - Gradually increase board size and complexity
    - Progress from lower to higher mine densities
  
  - **Reinforcement Learning**
    - Use rewards based on successful moves and game completion
    - Penalize triggering mines
    - Consider delayed rewards for full game outcomes
  
  - **Supervised Learning**
    - Generate optimal or near-optimal moves using the logic bot
    - Train network to predict these moves
    - Fine-tune with reinforcement learning
  
  - **Overfitting Prevention**
    - Augment data through board rotations and reflections
    - Use regularization techniques (dropout, weight decay)
    - Implement early stopping based on validation performance
    - Ensure sufficient diversity in training board configurations

- **Code Structure:**
  ```
  /minesweeper
  ├── environment/
  │   ├── game.py           # Game implementation
  │   └── utils.py          # Helper functions
  ├── agents/
  │   ├── logic_bot.py      # Basic logic bot implementation
  │   ├── neural_bot.py     # Neural network agent
  │   └── random_bot.py     # Random baseline agent
  ├── models/
  │   ├── cnn_model.py      # Convolutional models
  │   ├── sequential.py     # Sequential models
  │   └── attention.py      # Attention-based models
  ├── training/
  │   ├── data_generation.py # Generate training data
  │   ├── train.py          # Training loop implementation
  │   └── curriculum.py     # Curriculum learning
  ├── evaluation/
  │   ├── metrics.py        # Performance metrics
  │   └── compare.py        # Comparison with logic bot
  └── visualization/
      ├── game_viz.py       # Visualize game boards
      └── results_viz.py    # Plot performance results
  ```

- **Testing Requirements:**
  - Validate with statistically significant sample sizes (>=1000 games)
  - Generate confidence intervals for all metrics
  - Provide side-by-side comparisons with the logic bot
  - Document specific scenarios where neural bot makes different decisions 