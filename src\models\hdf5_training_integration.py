#!/usr/bin/env python3
"""
Enhanced HDF5 Training Integration for Minesweeper Deep Learning

This module provides enhanced training script integration with the optimized
HDF5 data loading pipeline, including automatic data discovery and validation.
"""

import sys
import os
import argparse
from typing import Tuple, Optional, Dict, Any

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import our HDF5 data loader
from hdf5_data_loader import load_data_with_fallback, HDF5DataLoader, JSONDataLoaderCompat

# Handle dependencies
try:
    import numpy as np
    import tensorflow as tf
    TRAINING_DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Training dependencies not available: {e}")
    TRAINING_DEPENDENCIES_AVAILABLE = False


def find_latest_data_file(difficulty: str, data_format: str = "auto") -> Optional[str]:
    """
    Find the latest data file for a given difficulty
    
    Args:
        difficulty: 'easy', 'intermediate', or 'expert'
        data_format: 'hdf5', 'json', or 'auto'
        
    Returns:
        Path to the latest data file or None if not found
    """
    
    # Define search directories
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    search_dirs = [
        os.path.join(project_root, "data", "hdf5"),
        os.path.join(project_root, "data", "hdf5_demo"),
        os.path.join(project_root, "data", "simulation"),
        os.path.join(project_root, "data", "test"),
        os.path.join(project_root, "src", "data", "simulation")
    ]
    
    # Define file extensions to search for
    if data_format == "hdf5":
        extensions = ['.h5', '.hdf5']
    elif data_format == "json":
        extensions = ['.json']
    else:  # auto
        extensions = ['.h5', '.hdf5', '.json']
    
    found_files = []
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        for filename in os.listdir(search_dir):
            # Check if file matches difficulty and extension
            if (difficulty in filename.lower() and 
                any(filename.endswith(ext) for ext in extensions)):
                
                filepath = os.path.join(search_dir, filename)
                mtime = os.path.getmtime(filepath)
                found_files.append((mtime, filepath))
    
    if not found_files:
        print(f"❌ No {difficulty} data files found in search directories")
        return None
    
    # Return the most recent file
    found_files.sort(reverse=True)
    latest_file = found_files[0][1]
    
    print(f"📁 Found latest {difficulty} data file: {os.path.basename(latest_file)}")
    return latest_file


def validate_data_compatibility(data_loader, difficulty: str) -> bool:
    """
    Validate that data is compatible with training requirements
    
    Args:
        data_loader: Data loader instance
        difficulty: Expected difficulty level
        
    Returns:
        True if compatible, False otherwise
    """
    
    print(f"🔍 Validating data compatibility for {difficulty}...")
    
    try:
        info = data_loader.get_dataset_info()
        
        # Expected configurations
        expected_configs = {
            "easy": (9, 9, 10),
            "intermediate": (16, 16, 40),
            "expert": (30, 16, 99)
        }
        
        if difficulty not in expected_configs:
            print(f"❌ Unknown difficulty: {difficulty}")
            return False
        
        expected_H, expected_W, expected_M = expected_configs[difficulty]
        actual_H = info['board_config']['height']
        actual_W = info['board_config']['width']
        
        # Validate board dimensions
        if actual_H != expected_H or actual_W != expected_W:
            print(f"❌ Board size mismatch: {actual_H}×{actual_W} vs expected {expected_H}×{expected_W}")
            return False
        
        # Validate data availability
        total_samples = info.get('total_samples', 0)
        if total_samples < 100:
            print(f"⚠️ Low sample count: {total_samples} (minimum 100 recommended)")
            return False
        
        print(f"✅ Data validation passed:")
        print(f"   Board: {actual_H}×{actual_W}")
        print(f"   Samples: {total_samples}")
        print(f"   File size: {info['file_size_mb']:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Data validation failed: {e}")
        return False


def create_optimized_datasets(data_loader, batch_size: int = 64, validation_split: float = 0.15) -> Tuple:
    """
    Create optimized TensorFlow datasets for training
    
    Args:
        data_loader: Data loader instance
        batch_size: Batch size for training
        validation_split: Fraction of data for validation
        
    Returns:
        Tuple of (train_dataset, val_dataset, dataset_info)
    """
    
    if not TRAINING_DEPENDENCIES_AVAILABLE:
        print("❌ Training dependencies not available")
        return None, None, None
    
    print(f"⚙️ Creating optimized datasets...")
    print(f"   Batch size: {batch_size}")
    print(f"   Validation split: {validation_split}")
    
    try:
        # Get dataset info
        info = data_loader.get_dataset_info()
        
        # Create datasets using the data loader
        if hasattr(data_loader, 'create_tf_dataset'):
            train_dataset, val_dataset = data_loader.create_tf_dataset(
                batch_size=batch_size,
                validation_split=validation_split,
                shuffle=True
            )
        else:
            # Fallback for JSON compatibility mode
            print("⚠️ Using fallback dataset creation")
            
            def data_generator():
                for states_chunk, moves_chunk in data_loader.hdf5_chunked_generator(
                    chunk_size=batch_size, shuffle=True
                ):
                    if TRAINING_DEPENDENCIES_AVAILABLE:
                        for i in range(len(states_chunk)):
                            yield states_chunk[i], moves_chunk[i]
            
            H, W = info['board_config']['height'], info['board_config']['width']
            
            dataset = tf.data.Dataset.from_generator(
                data_generator,
                output_signature=(
                    tf.TensorSpec(shape=(H, W, 12), dtype=tf.float32),
                    tf.TensorSpec(shape=(H, W), dtype=tf.float32)
                )
            )
            
            total_samples = info.get('total_samples', 1000)
            train_size = int(total_samples * (1 - validation_split))
            
            train_dataset = dataset.take(train_size).batch(batch_size)
            val_dataset = dataset.skip(train_size).batch(batch_size)
            
            train_dataset = train_dataset.prefetch(tf.data.AUTOTUNE)
            val_dataset = val_dataset.prefetch(tf.data.AUTOTUNE)
        
        print(f"✅ Datasets created successfully")
        
        return train_dataset, val_dataset, info
        
    except Exception as e:
        print(f"❌ Dataset creation failed: {e}")
        return None, None, None


def preprocess_data_enhanced(state, move):
    """
    Enhanced preprocessing function with mine density normalization
    
    Args:
        state: Game state tensor (H, W, 12)
        move: Move target tensor (H, W)
        
    Returns:
        Tuple of (processed_state, processed_move)
    """
    
    if not TRAINING_DEPENDENCIES_AVAILABLE:
        return state, move
    
    # Mine density normalization (channel 11 if present)
    if state.shape[-1] > 11:
        mine_density_channel = state[..., 11:12]
        normalized_density = tf.clip_by_value(mine_density_channel, 0.0, 0.5) / 0.5
        state = tf.concat([state[..., :11], normalized_density, state[..., 12:]], axis=-1)
    
    # Ensure proper data types
    state = tf.cast(state, tf.float32)
    move = tf.cast(move, tf.float32)
    
    # Flatten move for training (if needed)
    move_shape = tf.shape(move)
    if len(move.shape) > 1:
        move = tf.reshape(move, [-1])
    
    return state, move


def test_training_pipeline(difficulty: str, epochs: int = 2, batch_size: int = 64) -> bool:
    """
    Test the complete training pipeline with HDF5 data
    
    Args:
        difficulty: Difficulty level to test
        epochs: Number of epochs for testing
        batch_size: Batch size for testing
        
    Returns:
        True if test successful, False otherwise
    """
    
    print(f"\n🧪 Testing Training Pipeline for {difficulty.title()}")
    print("=" * 60)
    
    if not TRAINING_DEPENDENCIES_AVAILABLE:
        print("❌ Training dependencies not available")
        return False
    
    try:
        # Find data file
        data_file = find_latest_data_file(difficulty)
        if not data_file:
            return False
        
        # Load data
        data_loader = load_data_with_fallback(data_file)
        if not data_loader:
            print("❌ Could not create data loader")
            return False
        
        with data_loader as loader:
            # Validate compatibility
            if not validate_data_compatibility(loader, difficulty):
                return False
            
            # Create datasets
            train_dataset, val_dataset, info = create_optimized_datasets(
                loader, batch_size=batch_size
            )
            
            if train_dataset is None:
                print("❌ Dataset creation failed")
                return False
            
            # Apply preprocessing
            train_dataset = train_dataset.map(preprocess_data_enhanced)
            val_dataset = val_dataset.map(preprocess_data_enhanced)
            
            print(f"✅ Training pipeline test successful for {difficulty}")
            print(f"   Data file: {os.path.basename(data_file)}")
            print(f"   Board: {info['board_config']['height']}×{info['board_config']['width']}")
            print(f"   Samples: {info.get('total_samples', 'unknown')}")
            
            return True
            
    except Exception as e:
        print(f"❌ Training pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function for testing training integration"""
    parser = argparse.ArgumentParser(description='Test HDF5 training integration')
    
    parser.add_argument('--difficulty', choices=['easy', 'intermediate', 'expert'],
                       help='Test specific difficulty')
    parser.add_argument('--all', action='store_true',
                       help='Test all difficulties')
    parser.add_argument('--epochs', type=int, default=2,
                       help='Number of epochs for testing (default: 2)')
    parser.add_argument('--batch-size', type=int, default=64,
                       help='Batch size for testing (default: 64)')
    
    args = parser.parse_args()
    
    if not args.difficulty and not args.all:
        args.all = True  # Default to testing all
    
    difficulties = []
    if args.all:
        difficulties = ['easy', 'intermediate', 'expert']
    elif args.difficulty:
        difficulties = [args.difficulty]
    
    print("🔍 Testing HDF5 Training Integration")
    print("=" * 60)
    
    results = {}
    for difficulty in difficulties:
        results[difficulty] = test_training_pipeline(
            difficulty, epochs=args.epochs, batch_size=args.batch_size
        )
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Training Integration Test Results")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for difficulty, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {difficulty.title()} Training Pipeline")
    
    print(f"\n📈 Overall Results: {passed}/{total} pipelines tested successfully")
    
    if passed == total:
        print("🎉 All training pipelines ready for HDF5 data!")
    else:
        print("⚠️ Some training pipelines need attention.")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    exit(main())
