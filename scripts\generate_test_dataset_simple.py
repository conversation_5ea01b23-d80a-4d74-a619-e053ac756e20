#!/usr/bin/env python3
"""
Simple Test Dataset Generation Script (No External Dependencies)

This script generates test datasets without requiring h5py or numpy,
using only Python standard library and the existing codebase.
"""

import sys
import os
import argparse
import json
import random
from datetime import datetime

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def generate_simple_test_data(difficulty, games, bot_type="BayesBot"):
    """Generate test data using only standard library"""
    
    # Define difficulty configurations
    difficulty_configs = {
        "easy": (9, 9, 10),
        "intermediate": (16, 16, 40),
        "expert": (30, 16, 99)
    }
    
    if difficulty not in difficulty_configs:
        raise ValueError(f"Unknown difficulty: {difficulty}")
    
    H, W, M = difficulty_configs[difficulty]
    print(f"\n🎯 Generating Simple Test Dataset")
    print(f"Difficulty: {difficulty} ({H}x{W}, {M} mines)")
    print(f"Games: {games}")
    print(f"Bot: {bot_type}")
    
    try:
        # Import required modules
        from src.game.MineSweeper import MineSweeper
        from src.bots.BayesBot import BayesBot
        from src.bots.SimpleLogicBot import SimpleLogicBot
        
        # Wrapper for SimpleLogicBot
        class SimpleLogicBotWrapper(SimpleLogicBot):
            def __init__(self, game):
                super().__init__(game.H, game.W, game.M)
                self.update_from_game(game)
        
        # Collect data from games
        all_game_data = []
        total_steps = 0
        successful_games = 0
        
        print(f"\n📊 Playing {games} games...")
        
        for game_idx in range(games):
            try:
                # Initialize game
                game = MineSweeper(H=H, W=W, M=M)
                game.start()
                
                # Initialize bot
                if bot_type == "BayesBot":
                    bot = BayesBot(game)
                else:
                    bot = SimpleLogicBotWrapper(game)
                
                # Play game and collect data
                game_states = []
                game_moves = []
                step_count = 0
                
                while not game.game_over and step_count < 100:  # Safety limit
                    # Get current state
                    current_state = game.get_board_state_for_nn()
                    
                    # Get bot move
                    move = bot.make_move(game)
                    if move is None:
                        break
                    
                    # Create move target (one-hot encoding)
                    move_target = [[0.0 for _ in range(W)] for _ in range(H)]
                    move_target[move[0]][move[1]] = 1.0
                    
                    # Store state and move
                    game_states.append(current_state.tolist())  # Convert to list
                    game_moves.append(move_target)
                    
                    # Execute move
                    result = game.make_move(move[0], move[1])
                    step_count += 1
                    
                    if result.get("mine_triggered", False) or result.get("won", False):
                        break
                
                if len(game_states) > 0:
                    game_data = {
                        'game_index': game_idx,
                        'states': game_states,
                        'moves': game_moves,
                        'steps': len(game_states),
                        'won': game.check_win_condition(),
                        'board_config': (H, W, M)
                    }
                    all_game_data.append(game_data)
                    total_steps += len(game_states)
                    successful_games += 1
                    
                    if (game_idx + 1) % max(1, games // 4) == 0:
                        print(f"  Progress: {game_idx + 1}/{games} games, {total_steps} steps collected")
                
            except Exception as e:
                print(f"  Warning: Game {game_idx} failed: {e}")
                continue
        
        if successful_games == 0:
            raise RuntimeError("No games completed successfully!")
        
        print(f"\n✅ Data Collection Complete")
        print(f"Successful games: {successful_games}/{games}")
        print(f"Total steps: {total_steps}")
        print(f"Average steps per game: {total_steps/successful_games:.1f}")
        
        # Save data as JSON
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = "data/test"
        os.makedirs(output_dir, exist_ok=True)
        
        filename = f"test_dataset_{difficulty}_{bot_type}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)
        
        # Prepare metadata
        metadata = {
            "timestamp": timestamp,
            "difficulty": difficulty,
            "board_height": H,
            "board_width": W,
            "num_mines": M,
            "target_num_games": games,
            "successful_games": successful_games,
            "total_steps": total_steps,
            "bot_class": bot_type,
            "data_format_version": "simple_test_v1.0"
        }
        
        # Save complete dataset
        dataset = {
            "metadata": metadata,
            "games": all_game_data
        }
        
        print(f"\n💾 Saving to: {filepath}")
        
        with open(filepath, 'w') as f:
            json.dump(dataset, f, indent=2)
        
        file_size_mb = os.path.getsize(filepath) / (1024*1024)
        print(f"✅ Test dataset saved successfully!")
        print(f"File size: {file_size_mb:.2f} MB")
        
        return filepath, {
            'difficulty': difficulty,
            'games': successful_games,
            'steps': total_steps,
            'filepath': filepath,
            'metadata': metadata
        }
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Creating mock data instead...")
        return generate_mock_data(difficulty, games, bot_type)
    except Exception as e:
        print(f"❌ Error generating test data: {e}")
        return None, None


def generate_mock_data(difficulty, games, bot_type):
    """Generate mock test data when imports fail"""
    
    difficulty_configs = {
        "easy": (9, 9, 10),
        "intermediate": (16, 16, 40),
        "expert": (30, 16, 99)
    }
    
    H, W, M = difficulty_configs[difficulty]
    
    print(f"\n🎭 Generating Mock Test Data")
    print(f"Difficulty: {difficulty} ({H}x{W}, {M} mines)")
    
    # Create mock game data
    mock_games = []
    total_steps = 0
    
    for game_idx in range(games):
        # Mock game with 3-8 steps
        num_steps = random.randint(3, 8)
        
        game_states = []
        game_moves = []
        
        for step in range(num_steps):
            # Mock state: H x W x 12 channels
            mock_state = []
            for r in range(H):
                row = []
                for c in range(W):
                    # 12 channels with random values
                    cell = [random.random() for _ in range(12)]
                    row.append(cell)
                mock_state.append(row)
            
            # Mock move: one-hot encoded
            move_r, move_c = random.randint(0, H-1), random.randint(0, W-1)
            mock_move = [[0.0 for _ in range(W)] for _ in range(H)]
            mock_move[move_r][move_c] = 1.0
            
            game_states.append(mock_state)
            game_moves.append(mock_move)
        
        mock_game = {
            'game_index': game_idx,
            'states': game_states,
            'moves': game_moves,
            'steps': num_steps,
            'won': random.choice([True, False]),
            'board_config': (H, W, M)
        }
        
        mock_games.append(mock_game)
        total_steps += num_steps
    
    # Save mock data
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = "data/test"
    os.makedirs(output_dir, exist_ok=True)
    
    filename = f"mock_dataset_{difficulty}_{bot_type}_{timestamp}.json"
    filepath = os.path.join(output_dir, filename)
    
    metadata = {
        "timestamp": timestamp,
        "difficulty": difficulty,
        "board_height": H,
        "board_width": W,
        "num_mines": M,
        "target_num_games": games,
        "successful_games": games,
        "total_steps": total_steps,
        "bot_class": f"Mock{bot_type}",
        "data_format_version": "mock_test_v1.0"
    }
    
    dataset = {
        "metadata": metadata,
        "games": mock_games
    }
    
    with open(filepath, 'w') as f:
        json.dump(dataset, f, indent=2)
    
    print(f"✅ Mock dataset saved: {filepath}")
    print(f"Games: {games}, Steps: {total_steps}")
    
    return filepath, {
        'difficulty': difficulty,
        'games': games,
        'steps': total_steps,
        'filepath': filepath,
        'metadata': metadata
    }


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description='Generate simple test datasets')
    
    parser.add_argument('--test-mode', action='store_true', required=True,
                       help='Enable test mode (required flag)')
    parser.add_argument('--games', type=int, default=10,
                       help='Number of games to generate (default: 10)')
    parser.add_argument('--difficulty', choices=['easy', 'intermediate', 'expert'], required=True,
                       help='Difficulty level')
    parser.add_argument('--bot', choices=['BayesBot', 'SimpleLogicBot'], default='BayesBot',
                       help='Bot to use for data generation (default: BayesBot)')
    
    args = parser.parse_args()
    
    try:
        filepath, summary = generate_simple_test_data(
            difficulty=args.difficulty,
            games=args.games,
            bot_type=args.bot
        )
        
        if filepath and summary:
            print(f"\n🎉 Test Dataset Generation Complete!")
            print(f"📁 File: {filepath}")
            print(f"📊 Summary: {summary['games']} games, {summary['steps']} steps")
            return 0
        else:
            print(f"\n❌ Test dataset generation failed")
            return 1
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
