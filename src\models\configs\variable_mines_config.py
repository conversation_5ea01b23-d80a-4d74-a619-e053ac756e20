#!/usr/bin/env python3
"""
Configuration for Variable Mine Counts Training (30x30, 0-30% density)
Optimized parameters for variable mine density learning.
"""

from ..base_trainer import TrainingConfig


class VariableMinesConfig(TrainingConfig):
    """Configuration for variable mine counts training"""
    
    # Model identification
    model_arch: str = "VariableMinesCNN"
    difficulty: str = "variable_mines"
    
    # Board configuration
    board_height: int = 30
    board_width: int = 30
    num_mines: int = 180  # Average for 20% density
    
    # Training hyperparameters - optimized for variable mine density
    batch_size: int = 64  # Reduced for large board size (30x30 = 900 cells)
    epochs: int = 40  # Increased epochs for variable mine density learning
    learning_rate: float = 0.001
    validation_split: float = 0.15
    
    # Callback configuration
    early_stopping_patience: int = 12  # Increased patience for variable mine density learning
    early_stopping_monitor: str = "val_move_accuracy"
    model_checkpoint_monitor: str = "val_move_accuracy"
    tensorboard_histogram_freq: int = 1
    
    # Data processing
    generator_chunk_size: int = 128  # Smaller chunk size for large boards
    shuffle_buffer_size: int = 5000
    
    # Data file pattern for auto-detection
    data_file_pattern: str = "*variable_mines*H30*W30*.h5"
    
    # Directory configuration
    models_dir: str = "models/trained_simple"
    logs_dir: str = "logs/variable_mines_training"
