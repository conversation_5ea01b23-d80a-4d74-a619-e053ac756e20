---
description: Guidelines for creating and maintaining Cursor rules to ensure consistency and effectiveness.
globs: .cursor/rules/*.mdc
alwaysApply: true
---

- **Required Rule Structure:**
  ```markdown
  ---
  description: Clear, one-line description of what the rule enforces
  globs: path/to/files/*.ext, other/path/**/*
  alwaysApply: boolean
  ---

  - **Main Points in Bold**
    - Sub-points with details
    - Examples and explanations
  ```

- **File References:**
  - Use `[filename](mdc:path/to/file)` ([filename](mdc:filename)) to reference files
  - Example: [prisma.mdc](mdc:.cursor/rules/prisma.mdc) for rule references
  - Example: [schema.prisma](mdc:prisma/schema.prisma) for code references

- **Code Examples:**
  - Use language-specific code blocks
  ```typescript
  // ✅ DO: Show good examples
  const goodExample = true;
  
  // ❌ DON'T: Show anti-patterns
  const badExample = false;
  ```

- **Rule Content Guidelines:**
  - Start with high-level overview
  - Include specific, actionable requirements
  - Show examples of correct implementation
  - Reference existing code when possible
  - Keep rules DRY by referencing other rules

- **Rule Maintenance:**
  - Update rules when new patterns emerge
  - Add examples from actual codebase
  - Remove outdated patterns
  - Cross-reference related rules

- **Best Practices:**
  - Use bullet points for clarity
  - Keep descriptions concise
  - Include both DO and DON'T examples
  - Reference actual code over theoretical examples
  - Use consistent formatting across rules

- **Project-Specific Rules:**
  - Create dedicated rules for specific project types
  - Reference them in related files where applicable
  - Example: [minesweeper.mdc](mdc:.cursor/rules/minesweeper.mdc) for neural network Minesweeper project
  - **Structure for Project Rules:**
    - Begin with overall project description and goals
    - Organize by task or module dependencies
    - Include implementation requirements and constraints
    - Provide examples of correct patterns/implementations
    - Cross-reference with development workflow rules
  
  - **Minesweeper Project Organization:**
    - Task 1 (Traditional Boards): Separate sections for Easy, Intermediate, Expert difficulty
    - Task 2 (Variable Mine Count): Structure by mine density categories
    - Task 3 (Variable Board Size): Organize by implementation approach
    - Cross-reference sections when approaches overlap
  
  - **Rule Cross-Referencing:**
    - Link related rules when providing implementation guidance
    - Example: "Follow [minesweeper.mdc](mdc:.cursor/rules/minesweeper.mdc) for model architecture"
    - Explicit references to implementation details in other rules
    - Avoid duplication by referencing existing patterns

- **Neural Network Implementation Guidelines:**
  - **Architecture Documentation:**
    - Define clear naming conventions for neural network modules
    - Document architecture with layer dimensions and activation functions
    - Include diagram references for complex structures
    - Example format:
    ```python
    # ✅ DO: Document model architecture clearly
    class MinesweeperCNN(nn.Module):
        """
        Convolutional neural network for Minesweeper board analysis.
        
        Architecture:
        - Input: [B, C, H, W] tensor representing board state
        - Conv layers: 3 layers with 32, 64, 128 filters (3x3 kernels)
        - Pooling: Max pooling after each convolution
        - Output: [B, H*W] tensor with probability for each cell
        """
        def __init__(self, input_channels, board_size):
            # Implementation...
    
    # ❌ DON'T: Leave architecture undocumented
    class MinesweeperNN(nn.Module):
        def __init__(self, input_size, output_size):
            # Undocumented implementation...
    ```
  
  - **Experiment Reporting:**
    - Record all experimental configurations in standard format
    - Document hyperparameter values and training procedures
    - Include baseline comparison results
    - Example format:
    ```python
    # ✅ DO: Use standardized experiment reporting
    experiment_config = {
        "model_name": "Minesweeper-CNN-v2",
        "board_size": (16, 16),
        "mine_count": 40,
        "training_games": 10000,
        "validation_games": 1000,
        "test_games": 1000,
        "hyperparameters": {
            "learning_rate": 0.001,
            "batch_size": 64,
            "epochs": 100,
            "early_stopping": {"patience": 10, "min_delta": 0.001}
        },
        "results": {
            "success_rate": 0.78,
            "average_survival_steps": 143.2,
            "compared_to_logic_bot": "+12% success rate"
        }
    }
    ```
  
  - **Performance Visualization:**
    - Create standardized plots for comparing neural vs logic bot
    - Include confidence intervals in all performance metrics
    - Generate visualization for decision patterns
    - Example:
    ```python
    # ✅ DO: Standardize visualization functions
    def plot_performance_comparison(neural_results, logic_results, metric="success_rate"):
        """
        Plot performance comparison between neural and logic bots.
        
        Args:
            neural_results: Dictionary of neural bot performance metrics
            logic_results: Dictionary of logic bot performance metrics
            metric: Metric to plot (success_rate, survival_steps, mines_triggered)
        
        Returns:
            matplotlib.Figure: Comparison plot with confidence intervals
        """
        # Implementation...
    ```
  
  - **Model Versioning and Comparison:**
    - Track model versions with clear numbering scheme
    - Document improvements between versions
    - Maintain baseline comparison across versions
    - Example format for version tracking:
    ```markdown
    # ✅ DO: Document model version improvements
    | Version | Description | Improvement over baseline |
    |---------|-------------|---------------------------|
    | v1.0    | Basic CNN   | Baseline                  |
    | v1.1    | Added batch norm | +3% success rate     |
    | v2.0    | Added attention | +7% success rate      |
    ```
  