#!/usr/bin/env python3
"""
Model Compatibility Testing Script for Minesweeper AI

This script tests model compatibility with data loading, preprocessing, and training
without requiring external dependencies like h5py or tensorflow.

Tests:
1. Data loading interface compatibility
2. Shape validation for input/output tensors
3. Training pipeline structure verification
4. Model saving/loading interface testing
"""

import sys
import os
import numpy as np
from typing import Dict, List, Tuple, Any

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_game_engine():
    """Test basic game engine functionality"""
    print("🎮 Testing Game Engine...")
    
    try:
        from src.game.MineSweeper import MineSweeper
        
        # Test different difficulty configurations
        configs = [
            ("easy", 9, 9, 10),
            ("intermediate", 16, 16, 40),
            ("expert", 30, 16, 99)
        ]
        
        for difficulty, H, W, M in configs:
            game = MineSweeper(H=H, W=W, M=M)
            game.start()
            
            # Test board state generation
            state = game.get_board_state_for_nn()
            expected_shape = (H, W, 12)  # 12 channels
            
            if state.shape != expected_shape:
                print(f"❌ {difficulty}: Shape mismatch. Expected {expected_shape}, got {state.shape}")
                return False
            
            print(f"✅ {difficulty}: Game engine working, state shape {state.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Game engine test failed: {e}")
        return False


def test_bot_interfaces():
    """Test bot interface compatibility"""
    print("\n🤖 Testing Bot Interfaces...")
    
    try:
        from src.game.MineSweeper import MineSweeper
        from src.bots.BayesBot import BayesBot
        from src.bots.SimpleLogicBot import SimpleLogicBot
        
        # Test with easy configuration
        game = MineSweeper(H=9, W=9, M=10)
        game.start()
        
        # Test BayesBot
        bayes_bot = BayesBot(game)
        bayes_move = bayes_bot.make_move(game)
        
        if bayes_move is None or len(bayes_move) != 2:
            print(f"❌ BayesBot: Invalid move format: {bayes_move}")
            return False
        
        print(f"✅ BayesBot: Interface working, move format {type(bayes_move)}")
        
        # Test SimpleLogicBot
        class SimpleLogicBotWrapper(SimpleLogicBot):
            def __init__(self, game):
                super().__init__(game.H, game.W, game.M)
                self.update_from_game(game)
        
        logic_bot = SimpleLogicBotWrapper(game)
        logic_move = logic_bot.make_move(game)
        
        if logic_move is None or len(logic_move) != 2:
            print(f"❌ SimpleLogicBot: Invalid move format: {logic_move}")
            return False
        
        print(f"✅ SimpleLogicBot: Interface working, move format {type(logic_move)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Bot interface test failed: {e}")
        return False


def test_data_generation_interface():
    """Test data generation pipeline interface"""
    print("\n📊 Testing Data Generation Interface...")
    
    try:
        from src.game.MineSweeper import MineSweeper
        from src.bots.BayesBot import BayesBot
        from src.simulations import play_proba
        
        # Test single game data generation
        game = MineSweeper(H=9, W=9, M=10)
        game.start()
        bot = BayesBot(game)
        
        result = play_proba(game, bot, verbose=False, track_sequence=True, track_attention=True)
        
        if not result or 'states' not in result or 'moves' not in result:
            print(f"❌ Data generation: Invalid result format")
            return False
        
        states = result['states']
        moves = result['moves']
        
        if len(states) == 0 or len(moves) == 0:
            print(f"❌ Data generation: No data collected")
            return False
        
        if len(states) != len(moves):
            print(f"❌ Data generation: State/move count mismatch: {len(states)} vs {len(moves)}")
            return False
        
        # Check data shapes
        state_shape = states[0].shape if len(states) > 0 else None
        move_shape = moves[0].shape if len(moves) > 0 else None
        
        print(f"✅ Data generation: Working, collected {len(states)} steps")
        print(f"   State shape: {state_shape}, Move shape: {move_shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data generation test failed: {e}")
        return False


def test_training_script_structure():
    """Test training script structure and interfaces"""
    print("\n🏋️ Testing Training Script Structure...")
    
    training_scripts = [
        ("TM_easy.py", "easy"),
        ("TM_intermediate.py", "intermediate"), 
        ("TM_expert.py", "expert")
    ]
    
    results = {}
    
    for script_name, difficulty in training_scripts:
        script_path = f"src/models/{script_name}"
        
        if not os.path.exists(script_path):
            print(f"❌ {difficulty}: Script not found: {script_path}")
            results[difficulty] = False
            continue
        
        try:
            # Read script content to check for key components
            with open(script_path, 'r') as f:
                content = f.read()
            
            # Check for essential components
            required_components = [
                'preprocess_data',
                'BATCH_SIZE',
                'EPOCHS',
                'LEARNING_RATE',
                'hdf5_chunked_generator',
                'tf.data.Dataset',
                'model.fit'
            ]
            
            missing_components = []
            for component in required_components:
                if component not in content:
                    missing_components.append(component)
            
            if missing_components:
                print(f"❌ {difficulty}: Missing components: {missing_components}")
                results[difficulty] = False
            else:
                print(f"✅ {difficulty}: All required components present")
                results[difficulty] = True
                
        except Exception as e:
            print(f"❌ {difficulty}: Error reading script: {e}")
            results[difficulty] = False
    
    return all(results.values())


def test_model_architecture_compatibility():
    """Test model architecture definitions"""
    print("\n🏗️ Testing Model Architecture Compatibility...")
    
    try:
        # Check if model utilities exist
        model_utils_path = "src/models/model_utils.py"
        
        if not os.path.exists(model_utils_path):
            print(f"❌ Model utilities not found: {model_utils_path}")
            return False
        
        with open(model_utils_path, 'r') as f:
            content = f.read()
        
        # Check for essential model components
        required_functions = [
            'create_simple_cnn_model',
            'create_variable_size_model',
            'create_attention_model'
        ]
        
        missing_functions = []
        for func in required_functions:
            if func not in content:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"❌ Missing model functions: {missing_functions}")
            return False
        
        print(f"✅ Model architecture: All required functions present")
        return True
        
    except Exception as e:
        print(f"❌ Model architecture test failed: {e}")
        return False


def test_evaluation_framework():
    """Test evaluation framework compatibility"""
    print("\n📈 Testing Evaluation Framework...")
    
    try:
        eval_script_path = "src/evaluate_bots.py"
        
        if not os.path.exists(eval_script_path):
            print(f"❌ Evaluation script not found: {eval_script_path}")
            return False
        
        with open(eval_script_path, 'r') as f:
            content = f.read()
        
        # Check for essential evaluation components
        required_components = [
            'evaluate_bot_performance',
            'compare_bots',
            'MinesweeperNNAgent',
            'argparse',
            'main'
        ]
        
        missing_components = []
        for component in required_components:
            if component not in content:
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ Missing evaluation components: {missing_components}")
            return False
        
        print(f"✅ Evaluation framework: All required components present")
        return True
        
    except Exception as e:
        print(f"❌ Evaluation framework test failed: {e}")
        return False


def generate_mock_test_data():
    """Generate mock test data for compatibility testing"""
    print("\n🎭 Generating Mock Test Data...")
    
    try:
        # Create test data directory
        test_dir = "data/test"
        os.makedirs(test_dir, exist_ok=True)
        
        # Generate mock data for each difficulty
        difficulties = [
            ("easy", 9, 9, 10),
            ("intermediate", 16, 16, 40),
            ("expert", 30, 16, 99)
        ]
        
        mock_data_info = {}
        
        for difficulty, H, W, M in difficulties:
            # Generate mock states and moves
            num_samples = 50  # Small test dataset
            
            # Mock states: (num_samples, H, W, 12)
            mock_states = np.random.rand(num_samples, H, W, 12).astype(np.float32)
            
            # Mock moves: (num_samples, H, W) - one-hot encoded
            mock_moves = np.zeros((num_samples, H, W), dtype=np.float32)
            for i in range(num_samples):
                r, c = np.random.randint(0, H), np.random.randint(0, W)
                mock_moves[i, r, c] = 1.0
            
            # Save as numpy files (since h5py not available)
            states_file = os.path.join(test_dir, f"mock_states_{difficulty}.npy")
            moves_file = os.path.join(test_dir, f"mock_moves_{difficulty}.npy")
            
            np.save(states_file, mock_states)
            np.save(moves_file, mock_moves)
            
            mock_data_info[difficulty] = {
                'states_file': states_file,
                'moves_file': moves_file,
                'shapes': {
                    'states': mock_states.shape,
                    'moves': mock_moves.shape
                },
                'board_config': (H, W, M)
            }
            
            print(f"✅ {difficulty}: Mock data generated - States: {mock_states.shape}, Moves: {mock_moves.shape}")
        
        return mock_data_info
        
    except Exception as e:
        print(f"❌ Mock data generation failed: {e}")
        return None


def run_compatibility_tests():
    """Run all compatibility tests"""
    print("🔍 Running Model Compatibility Tests")
    print("=" * 60)
    
    tests = [
        ("Game Engine", test_game_engine),
        ("Bot Interfaces", test_bot_interfaces),
        ("Data Generation", test_data_generation_interface),
        ("Training Scripts", test_training_script_structure),
        ("Model Architecture", test_model_architecture_compatibility),
        ("Evaluation Framework", test_evaluation_framework)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}: Test crashed: {e}")
            results[test_name] = False
    
    # Generate mock test data
    mock_data = generate_mock_test_data()
    results["Mock Data Generation"] = mock_data is not None
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Compatibility Test Results")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✅ PASS" if passed_test else "❌ FAIL"
        print(f"{status}: {test_name}")
        if passed_test:
            passed += 1
    
    print(f"\n📈 Overall Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All compatibility tests passed! System ready for training.")
        return True, mock_data
    else:
        print("⚠️  Some tests failed. Please review and fix issues before training.")
        return False, mock_data


def main():
    """Main function"""
    success, mock_data = run_compatibility_tests()
    
    if success and mock_data:
        print(f"\n📋 Mock Test Data Generated:")
        for difficulty, info in mock_data.items():
            print(f"  {difficulty}: {info['shapes']['states']} states, {info['shapes']['moves']} moves")
            print(f"    Board: {info['board_config'][0]}x{info['board_config'][1]}, {info['board_config'][2]} mines")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
