#!/usr/bin/env python3
"""
Variable Size Trainer: Trainer for variable board sizes (K×K for K>5)
Implements task-specific model architecture and training logic.
"""

import tensorflow as tf
from typing import List
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.models.base_trainer import BaseTrainer


class VariableSizeTrainer(BaseTrainer):
    """Trainer for variable board sizes (K×K for K>5)"""
    
    def create_model(self) -> tf.keras.Model:
        """Create CNN model optimized for variable board sizes"""
        H, W = self.config.board_height, self.config.board_width
        
        print(f"🏗️  Creating {self.config.model_arch} model for variable board sizes (max {H}x{W})")
        
        # Use functional API for more flexibility with variable sizes
        inputs = tf.keras.layers.Input(shape=(None, None, 12), name='board_input')
        
        # Convolutional layers that can handle variable input sizes
        x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same', name='conv2d_1')(inputs)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_1')(x)
        
        x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same', name='conv2d_2')(x)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_2')(x)
        
        x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu', padding='same', name='conv2d_3')(x)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_3')(x)
        
        x = tf.keras.layers.Conv2D(256, (3, 3), activation='relu', padding='same', name='conv2d_4')(x)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_4')(x)
        x = tf.keras.layers.Dropout(0.25, name='dropout_1')(x)
        
        x = tf.keras.layers.Conv2D(512, (3, 3), activation='relu', padding='same', name='conv2d_5')(x)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_5')(x)
        
        x = tf.keras.layers.Conv2D(1024, (3, 3), activation='relu', padding='same', name='conv2d_6')(x)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_6')(x)
        x = tf.keras.layers.Dropout(0.3, name='dropout_2')(x)
        
        # Use 1x1 convolution to reduce to single channel
        x = tf.keras.layers.Conv2D(512, (1, 1), activation='relu', padding='same', name='conv2d_7')(x)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_7')(x)
        x = tf.keras.layers.Dropout(0.25, name='dropout_3')(x)
        
        x = tf.keras.layers.Conv2D(256, (1, 1), activation='relu', padding='same', name='conv2d_8')(x)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_8')(x)
        
        x = tf.keras.layers.Conv2D(128, (1, 1), activation='relu', padding='same', name='conv2d_9')(x)
        x = tf.keras.layers.BatchNormalization(name='batch_norm_9')(x)
        
        # Final 1x1 convolution to get logits
        logits = tf.keras.layers.Conv2D(1, (1, 1), padding='same', name='logits_conv')(x)
        
        # Flatten to get move probabilities
        flattened_logits = tf.keras.layers.Flatten(name='flatten_logits')(logits)
        
        # Apply softmax to get probabilities
        outputs = tf.keras.layers.Softmax(name='move_probs')(flattened_logits)
        
        model = tf.keras.Model(inputs=inputs, outputs=outputs, name=f'{self.config.model_arch}_VariableSize')
        
        # Print model summary
        print(f"📋 Model Summary:")
        model.summary()
        
        return model
    
    def get_loss_function(self):
        """Get loss function optimized for variable board sizes"""
        return tf.keras.losses.CategoricalCrossentropy(
            from_logits=False,
            label_smoothing=0.05,  # Light label smoothing for variable sizes
            name='categorical_crossentropy'
        )
    
    def get_metrics(self) -> List:
        """Get metrics for variable board size evaluation"""
        return [
            'move_accuracy',
            tf.keras.metrics.TopKCategoricalAccuracy(k=3, name='top_3_accuracy'),
            tf.keras.metrics.TopKCategoricalAccuracy(k=5, name='top_5_accuracy'),
            tf.keras.metrics.TopKCategoricalAccuracy(k=10, name='top_10_accuracy'),
            tf.keras.metrics.CategoricalCrossentropy(name='categorical_crossentropy_metric')
        ]
    
    def create_callbacks(self) -> List[tf.keras.callbacks.Callback]:
        """Create callbacks with ReduceLROnPlateau for variable size learning"""
        # Get base callbacks
        callbacks = super().create_callbacks()
        
        # Add ReduceLROnPlateau for variable size learning
        reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_move_accuracy',
            factor=0.3,
            patience=7,
            mode='max',
            min_lr=1e-8,
            verbose=1
        )
        callbacks.append(reduce_lr)
        
        return callbacks
    
    def _get_sample_count(self) -> int:
        """Get sample count from data file for variable board sizes"""
        # Try to get actual count from data file
        if self.config.data_file:
            try:
                import h5py
                with h5py.File(self.config.data_file, 'r') as f:
                    if 'moves' in f:
                        return len(f['moves'])
                    elif 'X' in f:
                        return len(f['X'])
            except Exception:
                pass
        
        # Default estimate for variable board sizes
        return 200000  # Typical for variable size training data
