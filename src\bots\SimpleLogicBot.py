# simple_logic_bot.py
import random
from typing import Set, Tuple, Dict, List, Optional

# Assuming MineSweeper class is in ../game/MineSweeper.py
# Adjust path if necessary
try:
    from game.MineSweeper import MineSweeper
except ImportError:
    # Fallback if running directly from a different structure
    print("Warning: Could not import MineSweeper from game.MineSweeper. Assuming it's available.")
    # Define a placeholder if needed for type hinting, but the script needs the real class
    class MineSweeper: pass


class SimpleLogicBot:
    """
    Implements the simple logic bot described in the Minesweeper project PDF.
    Rules:
    1. If (clue - #known_mines) == #unknown_neighbors, mark unknowns as mines.
    2. If (#neighbors - clue - #known_safe) == #unknown_neighbors, mark unknowns as safe.
    Selects an inferred safe cell if available, otherwise picks randomly from remaining unknown cells.
    """
    STEPS = [(-1, -1), (-1, 0), (-1, 1), (0, 1),
             (1, 1), (1, 0), (1, -1), (0, -1)]

    def __init__(self, H: int, W: int, M: int):
        """
        Initializes the bot with game dimensions.
        """
        self.H = H
        self.W = W
        self.M = M # Total mines (though not directly used by this simple logic)

        # --- <PERSON><PERSON>'s Internal State ---
        # Set of (r, c) tuples for all cells not yet revealed or inferred as mine
        self.cells_remaining: Set[Tuple[int, int]] = set((r, c) for r in range(H) for c in range(W))
        # Set of (r, c) tuples inferred as safe but not yet revealed
        self.inferred_safe: Set[Tuple[int, int]] = set()
        # Set of (r, c) tuples inferred as mines
        self.inferred_mine: Set[Tuple[int, int]] = set()
        # Dictionary storing { (r, c): clue_value } for revealed clue cells
        self.revealed_clues: Dict[Tuple[int, int], int] = {}
        # Track cells revealed by the game to update remaining/safe sets
        self.game_revealed: Set[Tuple[int, int]] = set()

    def _is_valid_cell(self, r: int, c: int) -> bool:
        """Checks if coordinates are within board bounds."""
        return 0 <= r < self.H and 0 <= c < self.W

    def get_neighbors(self, r: int, c: int) -> List[Tuple[int, int]]:
        """Returns a list of valid neighbor coordinates for cell (r, c)."""
        neighbors = []
        for dr, dc in self.STEPS:
            nr, nc = r + dr, c + dc
            if self._is_valid_cell(nr, nc):
                neighbors.append((nr, nc))
        return neighbors

    def update_from_game(self, game: MineSweeper):
        """
        Updates the bot's internal state based on the visible game state.
        """
        newly_revealed = False
        for r in range(self.H):
            for c in range(self.W):
                if game.revealed[r][c] and (r, c) not in self.game_revealed:
                    # --- Cell is newly revealed by the game ---
                    self.game_revealed.add((r, c))
                    newly_revealed = True

                    # Remove from remaining and inferred sets
                    if (r, c) in self.cells_remaining:
                        self.cells_remaining.remove((r, c))
                    if (r, c) in self.inferred_safe:
                        self.inferred_safe.remove((r, c))
                    # Should not be in inferred_mine if revealed as safe, but check anyway
                    if (r, c) in self.inferred_mine:
                         print(f"Warning: Game revealed cell {(r,c)} which bot inferred as mine.")
                         self.inferred_mine.remove((r,c)) # Correct inference if needed

                    # Store clue if it's not a mine
                    clue = game._mines_count[r][c] # Access the visible value
                    if clue >= 0: # It's a number 0-8
                        self.revealed_clues[(r, c)] = clue
                    # If it was a mine (clue == -2), the game loop will handle termination

        # Return True if any new information was gained
        return newly_revealed

    def run_inference_loop(self):
        """
        Repeatedly applies the two basic inference rules until no new
        inferences (safe or mine) can be made in a full pass.
        """
        while True:
            made_new_inference = False
            
            # Create copies to iterate over while potentially modifying originals
            current_clues = list(self.revealed_clues.items())

            for (r, c), clue in current_clues:
                neighbors = self.get_neighbors(r, c)
                total_neighbors = len(neighbors)

                # Categorize neighbors based on bot's current knowledge
                known_mines_in_neighbors: Set[Tuple[int, int]] = set()
                known_safe_in_neighbors: Set[Tuple[int, int]] = set() # Revealed or inferred safe
                unknown_neighbors: Set[Tuple[int, int]] = set()

                for nr, nc in neighbors:
                    coord = (nr, nc)
                    if coord in self.inferred_mine:
                        known_mines_in_neighbors.add(coord)
                    elif coord in self.game_revealed or coord in self.inferred_safe:
                         # Treat game-revealed cells and inferred safe cells as 'known safe' for Rule 2
                         known_safe_in_neighbors.add(coord)
                    elif coord in self.cells_remaining:
                        # Only cells truly unknown to the bot
                        unknown_neighbors.add(coord)
                    # Ignore cells that are somehow not in any category (shouldn't happen)


                num_unknown = len(unknown_neighbors)
                if num_unknown == 0:
                    continue # No unknowns around this clue to infer about

                # --- Rule 1: Infer Mines ---
                # If the number of mines still needed (clue - known_mines)
                # equals the number of unknown neighbors, they must all be mines.
                mines_needed = clue - len(known_mines_in_neighbors)
                if mines_needed == num_unknown:
                    for ur, uc in unknown_neighbors:
                        if (ur, uc) not in self.inferred_mine:
                            # print(f"DEBUG: Rule 1 Infer Mine at {(ur, uc)} from clue at {(r,c)}") # Debug
                            self.inferred_mine.add((ur, uc))
                            # Crucially, remove from cells_remaining
                            if (ur, uc) in self.cells_remaining:
                                self.cells_remaining.remove((ur, uc))
                            # If it was mistakenly marked safe, remove it
                            if (ur, uc) in self.inferred_safe:
                                self.inferred_safe.remove((ur, uc))
                            made_new_inference = True

                # --- Rule 2: Infer Safe ---
                # Number of safe neighbors required = total_neighbors - clue
                # If the number of safe neighbors needed equals the number of
                # unknown neighbors plus already known safe neighbors,
                # then all unknown neighbors must be safe.
                # Simplified: If (#neighbors - clue) == (#known_safe + #unknown), infer unknowns safe
                # Or: If (#neighbors - clue - #known_safe) == #unknown, infer unknowns safe
                safe_needed = total_neighbors - clue
                known_safe_count = len(known_safe_in_neighbors) # Count revealed + inferred safe neighbors
                
                if safe_needed - known_safe_count == num_unknown:
                     for ur, uc in unknown_neighbors:
                          if (ur, uc) not in self.inferred_safe:
                               # print(f"DEBUG: Rule 2 Infer Safe at {(ur, uc)} from clue at {(r,c)}") # Debug
                               self.inferred_safe.add((ur, uc))
                               # DO NOT remove from cells_remaining - safe cells still need to be clicked
                               made_new_inference = True


            # If a full pass over all clues yielded no new inferences, exit loop
            if not made_new_inference:
                break

    def select_move(self) -> Optional[Tuple[int, int]]:
        """
        Selects the next cell to reveal based on the simple logic bot rules.
        1. Choose an unrevealed cell from inferred_safe.
        2. If none, choose a random cell from cells_remaining with enhanced heuristics.
        Returns (r, c) tuple or None if no move is possible.
        """
        # First, try to pick from inferred safe cells that haven't been revealed yet
        available_safe = list(self.inferred_safe - self.game_revealed)

        if available_safe:
            # Pick one, doesn't matter which for simple bot
            move = random.choice(available_safe)
            # Remove from inferred_safe so we don't pick it again immediately
            # (though update_from_game would remove it next turn anyway)
            self.inferred_safe.remove(move)
            # print(f"DEBUG: SimpleBot selecting inferred safe: {move}") # Debug
            return move
        else:
            # No safe cells inferred, use enhanced random selection strategy
            available_remaining = list(self.cells_remaining - self.inferred_mine) # Ensure not picking inferred mines

            if available_remaining:
                # ENHANCED: Use heuristics to improve random selection quality
                move = self._select_random_with_heuristics(available_remaining)
                # print(f"DEBUG: SimpleBot selecting random remaining: {move}") # Debug
                return move
            else:
                # No moves left (should only happen if board is cleared or stuck)
                # print("DEBUG: SimpleBot has no moves left.") # Debug
                return None

    def _select_random_with_heuristics(self, available_cells: List[Tuple[int, int]]) -> Tuple[int, int]:
        """
        Enhanced random selection that prefers cells likely to provide more information.

        Heuristics applied (in order of preference):
        1. Prefer cells not adjacent to any revealed clues (exploration)
        2. Prefer corner and edge cells (often safer in early game)
        3. Fall back to pure random selection

        Args:
            available_cells: List of valid cells to choose from

        Returns:
            Selected cell coordinates (r, c)
        """
        if not available_cells:
            return None

        # Heuristic 1: Prefer cells not adjacent to revealed clues (exploration moves)
        non_adjacent_cells = []
        for r, c in available_cells:
            neighbors = self.get_neighbors(r, c)
            # Check if any neighbor is a revealed clue
            has_clue_neighbor = any((nr, nc) in self.revealed_clues for nr, nc in neighbors)
            if not has_clue_neighbor:
                non_adjacent_cells.append((r, c))

        # If we have cells not adjacent to clues, prefer them (80% of the time)
        if non_adjacent_cells and random.random() < 0.8:
            return random.choice(non_adjacent_cells)

        # Heuristic 2: Prefer corner and edge cells (often safer)
        corner_edge_cells = []
        for r, c in available_cells:
            # Corner cells
            if (r == 0 or r == self.H - 1) and (c == 0 or c == self.W - 1):
                corner_edge_cells.append((r, c))
            # Edge cells (not corners)
            elif r == 0 or r == self.H - 1 or c == 0 or c == self.W - 1:
                corner_edge_cells.append((r, c))

        # If we have corner/edge cells, prefer them (60% of the time)
        if corner_edge_cells and random.random() < 0.6:
            return random.choice(corner_edge_cells)

        # Fallback: Pure random selection
        return random.choice(available_cells)

    def make_move(self, game: MineSweeper) -> Optional[Tuple[int, int]]:
        """
        Called by the evaluation loop. Updates state, runs inference, selects move.
        Returns (row, col) or None.
        """
        # 1. Update knowledge from the game board
        self.update_from_game(game)

        # 2. Run inference rules repeatedly
        self.run_inference_loop()

        # 3. Select the next move
        move = self.select_move()

        return move

