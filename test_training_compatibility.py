#!/usr/bin/env python3
"""
Training Script Compatibility Testing

This script tests whether training scripts can load test data and run
without requiring full TensorFlow/numpy dependencies.
"""

import sys
import os
import json
from typing import Dict, List, Tuple, Any

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def load_test_dataset(filepath: str) -> Dict:
    """Load test dataset from JSON file"""
    print(f"📂 Loading test dataset: {filepath}")
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        return None
    
    try:
        with open(filepath, 'r') as f:
            dataset = json.load(f)
        
        metadata = dataset.get('metadata', {})
        games = dataset.get('games', [])
        
        print(f"✅ Dataset loaded successfully")
        print(f"   Difficulty: {metadata.get('difficulty', 'unknown')}")
        print(f"   Board: {metadata.get('board_height', '?')}x{metadata.get('board_width', '?')}")
        print(f"   Mines: {metadata.get('num_mines', '?')}")
        print(f"   Games: {metadata.get('successful_games', len(games))}")
        print(f"   Total steps: {metadata.get('total_steps', '?')}")
        
        return dataset
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return None


def validate_data_structure(dataset: Dict, difficulty: str) -> bool:
    """Validate dataset structure matches expected format"""
    print(f"\n🔍 Validating {difficulty} dataset structure...")
    
    if not dataset:
        print(f"❌ No dataset provided")
        return False
    
    # Check metadata
    metadata = dataset.get('metadata', {})
    if not metadata:
        print(f"❌ Missing metadata")
        return False
    
    # Check games
    games = dataset.get('games', [])
    if not games:
        print(f"❌ No games in dataset")
        return False
    
    # Validate first game structure
    first_game = games[0]
    required_fields = ['game_index', 'states', 'moves', 'steps', 'board_config']
    
    missing_fields = []
    for field in required_fields:
        if field not in first_game:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ Missing game fields: {missing_fields}")
        return False
    
    # Check data dimensions
    states = first_game['states']
    moves = first_game['moves']
    board_config = first_game['board_config']
    
    if len(states) != len(moves):
        print(f"❌ State/move count mismatch: {len(states)} vs {len(moves)}")
        return False
    
    if len(states) == 0:
        print(f"❌ No states in first game")
        return False
    
    # Check state dimensions
    first_state = states[0]
    H, W, expected_mines = board_config
    
    if len(first_state) != H:
        print(f"❌ State height mismatch: {len(first_state)} vs {H}")
        return False
    
    if len(first_state[0]) != W:
        print(f"❌ State width mismatch: {len(first_state[0])} vs {W}")
        return False
    
    if len(first_state[0][0]) != 12:
        print(f"❌ State channels mismatch: {len(first_state[0][0])} vs 12")
        return False
    
    # Check move dimensions
    first_move = moves[0]
    
    if len(first_move) != H:
        print(f"❌ Move height mismatch: {len(first_move)} vs {H}")
        return False
    
    if len(first_move[0]) != W:
        print(f"❌ Move width mismatch: {len(first_move[0])} vs {W}")
        return False
    
    print(f"✅ {difficulty} dataset structure validation passed")
    print(f"   States shape: ({len(states)}, {H}, {W}, 12)")
    print(f"   Moves shape: ({len(moves)}, {H}, {W})")
    print(f"   Board config: {board_config}")
    
    return True


def test_training_script_structure(difficulty: str) -> bool:
    """Test training script structure and key components"""
    print(f"\n🏋️ Testing {difficulty} training script structure...")
    
    script_path = f"src/models/TM_{difficulty}.py"
    
    if not os.path.exists(script_path):
        print(f"❌ Training script not found: {script_path}")
        return False
    
    try:
        with open(script_path, 'r') as f:
            content = f.read()
        
        # Check for essential components
        essential_components = {
            "Data Loading": ["h5py", "hdf5_chunked_generator"],
            "Preprocessing": ["preprocess_data", "mine_density_channel"],
            "Model Creation": ["create_simple_cnn_model", "input_shape"],
            "Training Config": ["BATCH_SIZE", "EPOCHS", "LEARNING_RATE"],
            "TensorFlow": ["tf.data.Dataset", "model.fit"]
        }
        
        results = {}
        
        for category, components in essential_components.items():
            found = sum(1 for comp in components if comp in content)
            total = len(components)
            results[category] = (found, total)
            
            status = "✅" if found == total else "⚠️" if found > 0 else "❌"
            print(f"  {status} {category}: {found}/{total} components found")
        
        # Overall assessment
        total_found = sum(found for found, total in results.values())
        total_expected = sum(total for found, total in results.values())
        
        success_rate = total_found / total_expected
        
        if success_rate >= 0.8:
            print(f"✅ {difficulty} script structure test passed ({success_rate:.1%})")
            return True
        else:
            print(f"⚠️ {difficulty} script structure test partial ({success_rate:.1%})")
            return False
        
    except Exception as e:
        print(f"❌ Error testing {difficulty} script: {e}")
        return False


def test_model_architecture_compatibility(difficulty: str) -> bool:
    """Test model architecture compatibility"""
    print(f"\n🏗️ Testing {difficulty} model architecture compatibility...")
    
    # Define expected configurations
    configs = {
        "easy": (9, 9, 10),
        "intermediate": (16, 16, 40),
        "expert": (30, 16, 99)
    }
    
    if difficulty not in configs:
        print(f"❌ Unknown difficulty: {difficulty}")
        return False
    
    H, W, M = configs[difficulty]
    
    # Expected tensor shapes
    input_shape = (H, W, 12)  # 12 channels
    output_shape = (H * W,)   # Flattened output
    
    print(f"✅ Expected input shape: {input_shape}")
    print(f"✅ Expected output shape: {output_shape}")
    print(f"✅ Board configuration: {H}x{W}, {M} mines")
    
    # Check if model utilities exist
    model_utils_path = "src/models/model_utils.py"
    
    if os.path.exists(model_utils_path):
        with open(model_utils_path, 'r') as f:
            content = f.read()
        
        if "create_simple_cnn_model" in content:
            print(f"✅ Model creation function found")
        else:
            print(f"⚠️ Model creation function missing")
    else:
        print(f"⚠️ Model utilities file missing")
    
    return True


def simulate_training_pipeline(difficulty: str, dataset: Dict) -> bool:
    """Simulate training pipeline without TensorFlow"""
    print(f"\n⚙️ Simulating {difficulty} training pipeline...")
    
    if not dataset:
        print(f"❌ No dataset provided")
        return False
    
    games = dataset.get('games', [])
    metadata = dataset.get('metadata', {})
    
    # Simulate data loading
    total_steps = sum(game['steps'] for game in games)
    print(f"✅ Data loading simulation: {len(games)} games, {total_steps} steps")
    
    # Simulate preprocessing
    H = metadata.get('board_height', 9)
    W = metadata.get('board_width', 9)
    
    print(f"✅ Preprocessing simulation: {H}x{W} board normalization")
    
    # Simulate model architecture
    input_size = H * W * 12
    output_size = H * W
    
    print(f"✅ Model architecture simulation: {input_size} → {output_size}")
    
    # Simulate training configuration
    batch_size = 256 if difficulty == "easy" else 256 if difficulty == "intermediate" else 64
    epochs = 25 if difficulty == "easy" else 35 if difficulty == "intermediate" else 45
    learning_rate = 0.0005 if difficulty == "easy" else 0.001 if difficulty == "intermediate" else 0.0008
    
    print(f"✅ Training config simulation:")
    print(f"   Batch size: {batch_size}")
    print(f"   Epochs: {epochs}")
    print(f"   Learning rate: {learning_rate}")
    
    # Simulate training loop
    print(f"✅ Training loop simulation: {epochs} epochs with {total_steps//batch_size} batches per epoch")
    
    # Simulate model saving
    model_filename = f"model_{difficulty}_test.h5"
    print(f"✅ Model saving simulation: {model_filename}")
    
    return True


def run_comprehensive_compatibility_test():
    """Run comprehensive compatibility testing"""
    print("🔍 Running Comprehensive Training Compatibility Test")
    print("=" * 60)
    
    difficulties = ["easy", "intermediate", "expert"]
    test_results = {}
    
    for difficulty in difficulties:
        print(f"\n{'='*20} {difficulty.upper()} TESTING {'='*20}")
        
        # Find test dataset file
        test_files = [f for f in os.listdir("data/test") if f"dataset_{difficulty}" in f]
        if not test_files:
            print(f"❌ No test dataset found for {difficulty}")
            test_results[difficulty] = False
            continue
        
        dataset_path = os.path.join("data/test", test_files[0])
        
        # Load and validate dataset
        dataset = load_test_dataset(dataset_path)
        structure_valid = validate_data_structure(dataset, difficulty)
        
        # Test training script
        script_valid = test_training_script_structure(difficulty)
        
        # Test model architecture
        arch_valid = test_model_architecture_compatibility(difficulty)
        
        # Simulate training pipeline
        pipeline_valid = simulate_training_pipeline(difficulty, dataset)
        
        # Overall result
        overall_result = all([structure_valid, script_valid, arch_valid, pipeline_valid])
        test_results[difficulty] = overall_result
        
        status = "✅ PASS" if overall_result else "⚠️ PARTIAL"
        print(f"\n{status} {difficulty.title()} compatibility test completed")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Compatibility Test Results Summary")
    print("=" * 60)
    
    passed = sum(1 for result in test_results.values() if result)
    total = len(test_results)
    
    for difficulty, result in test_results.items():
        status = "✅ PASS" if result else "⚠️ PARTIAL"
        print(f"{status}: {difficulty.title()} Training Compatibility")
    
    print(f"\n📈 Overall Results: {passed}/{total} difficulties fully compatible")
    
    if passed == total:
        print("🎉 All training scripts are compatible with test datasets!")
    else:
        print("⚠️ Some compatibility issues found, but core functionality works.")
    
    return passed == total


def main():
    """Main function"""
    success = run_comprehensive_compatibility_test()
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
