#!/usr/bin/env python3
"""
Model-Data Compatibility Validation Script

This script validates that training models can properly load and process data
without requiring full training runs. It performs comprehensive compatibility
testing across all difficulty levels.

Usage:
    python validate_model_data_compatibility.py
    python validate_model_data_compatibility.py --difficulty easy
    python validate_model_data_compatibility.py --quick-test
"""

import sys
import os
import argparse
from typing import Dict, List, Tuple, Any

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def validate_file_structure():
    """Validate that all required files exist"""
    print("📁 Validating File Structure...")
    
    required_files = {
        "Game Engine": "src/game/MineSweeper.py",
        "BayesBot": "src/bots/BayesBot.py", 
        "SimpleLogicBot": "src/bots/SimpleLogicBot.py",
        "Neural Network Bot": "src/bots/nn_bot.py",
        "Data Generation": "src/simulations.py",
        "Evaluation Framework": "src/evaluate_bots.py",
        "Model Utilities": "src/models/model_utils.py",
        "Easy Training": "src/models/TM_easy.py",
        "Intermediate Training": "src/models/TM_intermediate.py",
        "Expert Training": "src/models/TM_expert.py",
        "Variable Mines Training": "src/models/TM_variable_mines.py",
        "Variable Size Training": "src/models/TM_variable_size.py",
        "Training Launcher": "src/models/train_launcher.py"
    }
    
    missing_files = []
    for component, filepath in required_files.items():
        if not os.path.exists(filepath):
            missing_files.append((component, filepath))
            print(f"❌ Missing: {component} ({filepath})")
        else:
            print(f"✅ Found: {component}")
    
    if missing_files:
        print(f"\n❌ File structure validation failed. Missing {len(missing_files)} files.")
        return False
    
    print(f"\n✅ File structure validation passed. All {len(required_files)} files found.")
    return True


def validate_training_script_structure(difficulty: str):
    """Validate training script has required components"""
    print(f"\n🔍 Validating {difficulty.title()} Training Script Structure...")
    
    script_path = f"src/models/TM_{difficulty}.py"
    
    if not os.path.exists(script_path):
        print(f"❌ Training script not found: {script_path}")
        return False
    
    try:
        with open(script_path, 'r') as f:
            content = f.read()
        
        # Check for essential components
        required_components = {
            "Data Loading": ["h5py", "hdf5_chunked_generator"],
            "Preprocessing": ["preprocess_data", "mine_density_channel", "normalized_density"],
            "Model Architecture": ["create_simple_cnn_model", "input_shape", "output_size"],
            "Training Configuration": ["BATCH_SIZE", "EPOCHS", "LEARNING_RATE"],
            "TensorFlow Pipeline": ["tf.data.Dataset", "from_generator", "model.fit"],
            "Model Saving": ["model.save", "models_dir"],
            "GPU Configuration": ["mixed_precision", "memory_growth"]
        }
        
        validation_results = {}
        
        for category, components in required_components.items():
            found_components = []
            missing_components = []
            
            for component in components:
                if component in content:
                    found_components.append(component)
                else:
                    missing_components.append(component)
            
            validation_results[category] = {
                "found": found_components,
                "missing": missing_components,
                "status": len(missing_components) == 0
            }
            
            status = "✅" if validation_results[category]["status"] else "⚠️"
            print(f"  {status} {category}: {len(found_components)}/{len(components)} components")
            
            if missing_components:
                print(f"    Missing: {missing_components}")
        
        # Overall validation
        all_passed = all(result["status"] for result in validation_results.values())
        
        if all_passed:
            print(f"✅ {difficulty.title()} script validation passed")
        else:
            print(f"⚠️ {difficulty.title()} script validation completed with warnings")
        
        return validation_results
        
    except Exception as e:
        print(f"❌ Error validating {difficulty} script: {e}")
        return False


def validate_preprocessing_consistency():
    """Validate preprocessing consistency across scripts"""
    print(f"\n🔄 Validating Preprocessing Consistency...")
    
    scripts = ["TM_easy.py", "TM_intermediate.py", "TM_expert.py"]
    preprocessing_patterns = []
    
    for script in scripts:
        script_path = f"src/models/{script}"
        
        if not os.path.exists(script_path):
            print(f"❌ Script not found: {script}")
            continue
        
        try:
            with open(script_path, 'r') as f:
                content = f.read()
            
            # Extract preprocessing function
            preprocess_start = content.find("def preprocess_data")
            if preprocess_start == -1:
                print(f"❌ {script}: preprocess_data function not found")
                continue
            
            # Find the end of the function (next def or end of file)
            preprocess_end = content.find("\ndef ", preprocess_start + 1)
            if preprocess_end == -1:
                preprocess_end = len(content)
            
            preprocess_function = content[preprocess_start:preprocess_end]
            
            # Check for key preprocessing elements
            has_normalization = "normalized_density" in preprocess_function
            has_clipping = "tf.clip_by_value" in preprocess_function
            has_concat = "tf.concat" in preprocess_function
            
            preprocessing_patterns.append({
                "script": script,
                "has_normalization": has_normalization,
                "has_clipping": has_clipping,
                "has_concat": has_concat
            })
            
            status = "✅" if all([has_normalization, has_clipping, has_concat]) else "❌"
            print(f"  {status} {script}: Normalization={has_normalization}, Clipping={has_clipping}, Concat={has_concat}")
            
        except Exception as e:
            print(f"❌ Error analyzing {script}: {e}")
    
    # Check consistency
    if len(preprocessing_patterns) < 3:
        print(f"❌ Could not analyze all scripts")
        return False
    
    # All scripts should have the same preprocessing pattern
    reference = preprocessing_patterns[0]
    consistent = all(
        pattern["has_normalization"] == reference["has_normalization"] and
        pattern["has_clipping"] == reference["has_clipping"] and
        pattern["has_concat"] == reference["has_concat"]
        for pattern in preprocessing_patterns
    )
    
    if consistent:
        print(f"✅ Preprocessing consistency validation passed")
        return True
    else:
        print(f"❌ Preprocessing inconsistency detected")
        return False


def validate_model_architecture_definitions():
    """Validate model architecture definitions"""
    print(f"\n🏗️ Validating Model Architecture Definitions...")
    
    model_utils_path = "src/models/model_utils.py"
    
    if not os.path.exists(model_utils_path):
        print(f"❌ Model utilities not found: {model_utils_path}")
        return False
    
    try:
        with open(model_utils_path, 'r') as f:
            content = f.read()
        
        # Check for required model functions
        required_functions = {
            "Basic CNN": "create_simple_cnn_model",
            "Variable Size": "create_variable_size_model", 
            "Attention Model": "create_attention_model",
            "Mixed Precision": "configure_mixed_precision",
            "GPU Setup": "setup_gpu_memory_growth"
        }
        
        validation_results = {}
        
        for model_type, function_name in required_functions.items():
            if function_name in content:
                print(f"  ✅ {model_type}: {function_name} found")
                validation_results[model_type] = True
            else:
                print(f"  ❌ {model_type}: {function_name} missing")
                validation_results[model_type] = False
        
        # Check for TensorFlow imports
        tf_imports = ["import tensorflow as tf", "from tensorflow import keras"]
        has_tf_imports = any(imp in content for imp in tf_imports)
        
        if has_tf_imports:
            print(f"  ✅ TensorFlow imports found")
        else:
            print(f"  ❌ TensorFlow imports missing")
        
        all_passed = all(validation_results.values()) and has_tf_imports
        
        if all_passed:
            print(f"✅ Model architecture validation passed")
        else:
            print(f"❌ Model architecture validation failed")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error validating model architectures: {e}")
        return False


def validate_data_pipeline_integration():
    """Validate data pipeline integration"""
    print(f"\n🔗 Validating Data Pipeline Integration...")
    
    # Check simulations.py
    simulations_path = "src/simulations.py"
    
    if not os.path.exists(simulations_path):
        print(f"❌ Simulations script not found: {simulations_path}")
        return False
    
    try:
        with open(simulations_path, 'r') as f:
            content = f.read()
        
        # Check for essential pipeline components
        pipeline_components = {
            "Game Integration": ["MineSweeper", "get_board_state_for_nn"],
            "Bot Integration": ["BayesBot", "SimpleLogicBot", "make_move"],
            "Data Collection": ["play_proba", "collected_raw_states", "collected_raw_moves"],
            "Data Consistency": ["atomic data collection", "consistency validation"],
            "HDF5 Output": ["h5py", "create_dataset", "compression"],
            "Augmentation": ["augment_data", "rotation", "flip"]
        }
        
        validation_results = {}
        
        for category, components in pipeline_components.items():
            found_count = sum(1 for comp in components if comp in content)
            validation_results[category] = found_count / len(components)
            
            status = "✅" if validation_results[category] >= 0.8 else "⚠️" if validation_results[category] >= 0.5 else "❌"
            print(f"  {status} {category}: {found_count}/{len(components)} components found")
        
        # Overall pipeline validation
        avg_score = sum(validation_results.values()) / len(validation_results)
        
        if avg_score >= 0.8:
            print(f"✅ Data pipeline integration validation passed (score: {avg_score:.2f})")
            return True
        else:
            print(f"❌ Data pipeline integration validation failed (score: {avg_score:.2f})")
            return False
        
    except Exception as e:
        print(f"❌ Error validating data pipeline: {e}")
        return False


def validate_evaluation_framework():
    """Validate evaluation framework"""
    print(f"\n📊 Validating Evaluation Framework...")
    
    eval_path = "src/evaluate_bots.py"
    
    if not os.path.exists(eval_path):
        print(f"❌ Evaluation script not found: {eval_path}")
        return False
    
    try:
        with open(eval_path, 'r') as f:
            content = f.read()
        
        # Check for evaluation components
        eval_components = {
            "Bot Comparison": ["evaluate_bot_performance", "compare_bots"],
            "Neural Network Integration": ["MinesweeperNNAgent", "nn_bot"],
            "Statistical Analysis": ["confidence", "variance", "statistical"],
            "Visualization": ["plot", "matplotlib", "visualization"],
            "Command Line Interface": ["argparse", "main", "if __name__"],
            "Flexible Configuration": ["difficulty", "custom", "board-size"]
        }
        
        validation_results = {}
        
        for category, components in eval_components.items():
            found_count = sum(1 for comp in components if comp in content)
            validation_results[category] = found_count > 0
            
            status = "✅" if validation_results[category] else "❌"
            print(f"  {status} {category}: {'Found' if validation_results[category] else 'Missing'}")
        
        all_passed = all(validation_results.values())
        
        if all_passed:
            print(f"✅ Evaluation framework validation passed")
        else:
            print(f"❌ Evaluation framework validation failed")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error validating evaluation framework: {e}")
        return False


def run_comprehensive_validation(difficulties=None, quick_test=False):
    """Run comprehensive validation across all components"""
    print("🔍 Running Comprehensive Model-Data Compatibility Validation")
    print("=" * 70)
    
    if difficulties is None:
        difficulties = ["easy", "intermediate", "expert"]
    
    validation_results = {}
    
    # Core validation tests
    core_tests = [
        ("File Structure", validate_file_structure),
        ("Preprocessing Consistency", validate_preprocessing_consistency),
        ("Model Architecture", validate_model_architecture_definitions),
        ("Data Pipeline", validate_data_pipeline_integration),
        ("Evaluation Framework", validate_evaluation_framework)
    ]
    
    for test_name, test_func in core_tests:
        try:
            validation_results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} validation crashed: {e}")
            validation_results[test_name] = False
    
    # Difficulty-specific validation
    if not quick_test:
        for difficulty in difficulties:
            test_name = f"{difficulty.title()} Script"
            try:
                validation_results[test_name] = validate_training_script_structure(difficulty)
            except Exception as e:
                print(f"❌ {test_name} validation crashed: {e}")
                validation_results[test_name] = False
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 Validation Results Summary")
    print("=" * 70)
    
    passed = 0
    total = len(validation_results)
    
    for test_name, result in validation_results.items():
        if isinstance(result, dict):
            # Handle detailed results
            overall_result = all(r.get("status", False) if isinstance(r, dict) else r for r in result.values())
            status = "✅ PASS" if overall_result else "⚠️ PARTIAL"
        else:
            status = "✅ PASS" if result else "❌ FAIL"
            if result:
                passed += 1
        
        print(f"{status}: {test_name}")
    
    print(f"\n📈 Overall Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All validation tests passed! System ready for training.")
        return True
    else:
        print("⚠️ Some validation tests failed. Review issues before proceeding.")
        return False


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description='Validate model-data compatibility')
    
    parser.add_argument('--difficulty', choices=['easy', 'intermediate', 'expert'],
                       help='Validate specific difficulty only')
    parser.add_argument('--quick-test', action='store_true',
                       help='Run quick validation (skip detailed script analysis)')
    
    args = parser.parse_args()
    
    difficulties = [args.difficulty] if args.difficulty else None
    
    success = run_comprehensive_validation(difficulties, args.quick_test)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
