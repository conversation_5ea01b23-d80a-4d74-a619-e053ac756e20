# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Distribution / packaging
dist/
build/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Virtual environments
venv/
env/
ENV/
.venv/
*_env/
tf_gpu_env*/
directml_env/

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~
.spyderproject
.spyproject
.ropeproject
.projectile
.dir-locals.el

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb
!src/models/*.ipynb

# Project-specific
## Data
data/
*.h5
*.hdf5
*.npy
*.npz
*.csv
*.json
!requirements.json
!**/config.json
!**/settings.json

## HDF5 Implementation Generated Files
*_benchmark_results.json
*_benchmark_*.json
*_performance_analysis.json
*_conversion_plan.json
hdf5_structure_*.json
mock_dataset_*.json
minesweeper_sim_*.json

## Models
# Exception for src/models Python files
!src/models/
!src/models/**/*.py
# Trained model files
/models/
checkpoint/
checkpoints/
saved_models/

## Logs
logs/
logs_enhanced/
*.log
tensorboard/

## Temporary files
temp_*.py
*_temp.py
setup_*.ps1
setup_*.sh

## Backup files
*.backup
*.bak
*~
.#*
#*#

## Git workflow and development files
GIT_WORKFLOW_COMPLETION_REPORT.md
commit_message.txt
git_file_categorization.md
generate_hdf5_data.py
generate_test_dataset.py

# Operating System Files
## macOS
.DS_Store
.AppleDouble
.LSOverride
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

## Windows
Thumbs.db
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

## Linux
.directory
.Trash-*
.nfs*

## WSL specific
*.wslconfig
*:Zone.Identifier

# Task Master related
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
tasks.json
tasks/ 