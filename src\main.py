import random
import time # For potential pauses if needed
import numpy as np # For handling probabilities array
import sys # To potentially adjust import paths

# --- Adjust paths if your structure requires it ---
# Add the src directory to the Python path
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Make sure these point to your actual class locations
try:
    from src.bots.BayesBot import BayesBot  # Using bug-free implementation
    from src.game.MineSweeper import MineSweeper, Inference # Import Inference enum
    # Import the new SimpleLogicBot
    from src.bots.SimpleLogicBot import SimpleLogicBot  # Using faithful project specification implementation
except ImportError as e:
     print(f"Import Error: {e}")
     print("Please ensure BayesBot, MineSweeper, and SimpleLogicBot classes are accessible.")
     print("You might need to adjust Python's import path (sys.path) or your file structure.")
     sys.exit(1) # Exit if core components can't be imported


def choose_difficulty():
    """Prompts the user to select a difficulty level or enter custom board specifications."""
    board_size, num_mines = (9, 9), 10
    print("\n--- Select Difficulty ---")
    print("1. Easy (9x9, 10 mines)")
    print("2. Intermediate (16x16, 40 mines)")
    print("3. Expert (30x16, 99 mines)")
    print("4. Custom")
    choice = input("Enter your choice (1-4): ")

    if choice == '1':
        board_size, num_mines = (9, 9), 10
    elif choice == '2':
        board_size, num_mines = (16, 16), 40
    elif choice == '3':
        # Note: Minesweeper standard expert is 16x30 or 30x16
        board_size, num_mines = (30, 16), 99
    elif choice == '4':
        while True:
            try:
                custom_rows = int(input("Enter number of rows (e.g., 5-50): "))
                custom_cols = int(input("Enter number of columns (e.g., 5-50): "))
                if custom_rows <= 0 or custom_cols <= 0:
                    print("Dimensions must be positive.")
                    continue
                # Suggest a reasonable mine density range
                custom_mines_percentage = float(
                    input("Enter percentage of mines (e.g., 10-25): "))
                if not (0 < custom_mines_percentage < 100):
                     print("Percentage must be between 0 and 100 (exclusive).")
                     continue

                num_tiles = custom_rows * custom_cols
                num_mines = int(num_tiles * custom_mines_percentage / 100)

                # Ensure valid number of mines (at least 1, leave space for safe start)
                num_mines = max(1, num_mines)
                # Ensure at least 9 cells can be safe for the start
                if num_mines >= num_tiles - 8:
                    num_mines = num_tiles - 9
                    print(f"Mine count too high, reduced to {num_mines} to allow safe start.")
                if num_mines <= 0:
                    num_mines = 1
                    print("Setting minimum 1 mine.")


                board_size = (custom_rows, custom_cols)
                break # Valid custom input
            except ValueError:
                print("Invalid input. Please enter numbers.")
    else:
        print("Invalid choice! Please enter a number between 1 and 4.")
        return choose_difficulty() # Recurse

    print(f"Selected: {board_size[0]}x{board_size[1]} board with {num_mines} mines.")
    return board_size[0], board_size[1], num_mines


def run_simulations(bot_type):
    """Initiates Minesweeper games using the specified bot type and reports results."""
    H, W, num_mines = choose_difficulty()
    game_played = 0
    win_count = 0
    loss_count = 0  # Track losses explicitly
    total_moves = 0

    while True:
        try:
            num_games_to_play_str = input(f"Enter number of games to simulate (e.g., 10, 100): ")
            num_games_to_play = int(num_games_to_play_str)
            if num_games_to_play <= 0:
                print("Please enter a positive number of games.")
                continue
            break
        except ValueError:
            print("Invalid number.")

    bot_name = bot_type.__name__ # Get class name for display
    print(f"\n--- Starting {num_games_to_play} Game(s) with {bot_name} ---")

    for i in range(num_games_to_play):
        game_played += 1
        # Set verbose=False for simulations, True for single game analysis
        verbose_play = (num_games_to_play == 1)
        print(f"\n--- Game {game_played}/{num_games_to_play} ---")

        try:
            # Select the appropriate play function based on bot_type
            if bot_type == BayesBot:
                game_result = play_bayes_bot(H, W, num_mines, verbose=verbose_play)
            elif bot_type == SimpleLogicBot:
                game_result = play_simple_logic_bot(H, W, num_mines, verbose=verbose_play)
            else:
                 print(f"Error: Unknown bot type {bot_name}")
                 continue # Skip this game

            # Unpack results (assuming consistent return format)
            moves, won, mine_triggered = game_result["moves"], game_result["won"], game_result["mine_triggered"]

            total_moves += moves
            if won:
                win_count += 1
                print(f"Game {game_played}: WIN in {moves} moves.")
            else:
                loss_count += 1
                # Check if loss was due to mine or bot giving up/error
                if mine_triggered:
                    print(f"Game {game_played}: LOSS in {moves} moves (hit a mine).")
                else:
                    print(f"Game {game_played}: LOSS/Stuck after {moves} moves (no mine hit).")


        except ValueError as e:
            print(f"Error during game {game_played}: {e}. Skipping game.")
            continue
        except Exception as e: # Catch other potential errors during play
            print(f"Unexpected error during game {game_played}: {e}. Skipping game.")
            import traceback
            traceback.print_exc() # Print stack trace for debugging
            continue

    if game_played > 0:
        win_rate = (win_count / game_played) * 100
        avg_moves_per_game = total_moves / game_played
        print(f"\n--- {bot_name} Simulation Summary ---")
        print(f"Games Played: {game_played}")
        print(f"Games Won: {win_count}")
        print(f"Games Lost: {loss_count}")
        print(f"Win Rate: {win_rate:.2f}%")
        print(f"Average Moves per Game: {avg_moves_per_game:.1f}")

        # Optional: Add specific difficulty benchmarks if desired
        # if H == 9 and W == 9 and num_mines == 10: print("EASY MODE...")
        # elif H == 16 and W == 16 and num_mines == 40: print("INTERMEDIATE MODE...")
        # elif H == 30 and W == 16 and num_mines == 99: print("EXPERT MODE...")

    else:
        print("No games were completed.")


def update_game_inference_simple(game: MineSweeper, bot: SimpleLogicBot):
    """Updates the game's display inference layer based on the SimpleLogicBot's state."""
    # Reset game inferences for cells that aren't revealed or flagged
    for r in range(game.H):
        for c in range(game.W):
            if not game.revealed[r][c] and not game.flagged[r][c]:
                game.inference[r][c] = Inference.NONE # Default to unknown

    # Update with bot's SAFE inferences
    for r, c in bot.inferred_safe:
        if 0 <= r < game.H and 0 <= c < game.W and not game.revealed[r][c]:
            game.inference[r][c] = Inference.SAFE

    # Update with bot's MINE inferences
    for r, c in bot.inferred_mine:
        if 0 <= r < game.H and 0 <= c < game.W and not game.revealed[r][c] and not game.flagged[r][c]:
            game.inference[r][c] = Inference.MINE
    # No debug print here to keep simulation output cleaner


def play_simple_logic_bot(H, W, num_mines, verbose=True):
    """Plays a single Minesweeper game using the SimpleLogicBot."""
    number_of_moves = 0
    mine_triggered = False

    # Initialize Game and Bot
    try:
        game = MineSweeper(H, W, num_mines)
        simple_bot = SimpleLogicBot(H, W, num_mines) # Instantiate the simple bot
        game.start() # Make the first safe move
        simple_bot.update_from_game(game) # Initial update for the bot
    except ValueError as e:
        print(f"Error initializing game: {e}")
        raise

    if verbose:
        print("--- Starting SimpleLogicBot Game ---")
        update_game_inference_simple(game, simple_bot) # Show initial state with inferences
        print(game)

    # --- Game Loop ---
    while not game.game_over:
        number_of_moves += 1

        # --- Bot Decision ---
        # make_move updates state, runs inference, selects move
        move_coord = simple_bot.make_move(game)

        # Update game display inference *after* bot makes decision
        update_game_inference_simple(game, simple_bot)

        if move_coord is None:
            if verbose:
                print("SimpleLogicBot cannot find a move (likely stuck or won).")
            # Check if the game is actually won, otherwise it's stuck
            if not game.check_win_condition():
                 print("Bot stuck!")
                 # Optional: Reveal board to see why
                 # game.reveal_all()
                 # print(game)
            break # Exit loop if bot gives up or game ends logically

        x, y = move_coord

        if verbose:
            print(f"\n--- Move {number_of_moves}: SimpleBot reveals ({x}, {y}) ---")

        # --- Execute Move ---
        result = game.make_move(x, y) # Handles reveal and updates game state

        if result["mine_triggered"]:
            mine_triggered = True
            if verbose:
                print("**** MINE TRIGGERED ****")
            break # Exit loop

        if verbose:
            print(game) # Print board state after move

        # Check win condition explicitly after move (game.game_over might be set by win)
        if game.win_condition_met:
             if verbose: print("**** WIN CONDITION MET ****")
             break


    # --- End Game ---
    if verbose:
        game.reveal_all()
        print("\n--- Game Summary ---")
        print(game)
        print(f"Total Moves: {number_of_moves}")
        if game.win_condition_met and not mine_triggered:
            print("Game WON!")
        elif mine_triggered:
            print("Game LOST - Mine triggered!")
        else: # Bot got stuck or other termination
             print("Game LOST or Terminated (no win).")


    won = game.win_condition_met and not mine_triggered

    return {
        "moves": number_of_moves,
        "won": won,
        "mine_triggered": mine_triggered,
        # Add other stats if needed, e.g., number of random moves made
    }


# Renamed original play_proba to play_bayes_bot for clarity
def play_bayes_bot(H, W, num_mines, verbose=True):
    """Plays a single Minesweeper game using the Bayesian probability strategy (BayesBot)."""
    # Data collection for analysis (not NN training format)
    states_history = [] # History of game.values
    probabilities_history = [] # History of bot.probabilities

    number_of_moves = 0
    total_probability_sum = 0 # Sum of probabilities over moves (for avg)
    mine_triggered = False  # Track if any mine was triggered during the game

    # Initialize Game and Bot
    try:
        game = MineSweeper(H, W, num_mines)
        bayes_bot = BayesBot(game) # Bot holds reference to game
        game.start() # Make the first safe move
        bayes_bot.update_from_game(game) # Initial update for BayesBot
    except ValueError as e:
        print(f"Error initializing game: {e}")
        raise # Re-raise to be caught by the caller loop

    if verbose:
        print("--- Starting BayesBot Game ---")
        # update_game_inference(game, bayes_bot) # Use the BayesBot specific update if needed
        print(game)

    # --- Game Loop ---
    while not game.game_over:
        number_of_moves += 1

        # --- Bot Decision ---
        move_coord = bayes_bot.make_move(game) # Calls inference internally

        # Update game display inference *after* bot makes decision
        # update_game_inference(game, bayes_bot) # Use the BayesBot specific update if needed

        if move_coord is None:
            if verbose:
                print("BayesBot cannot find a move (game likely stuck, won, or lost based on bot logic).")
            if not game.check_win_condition(): print("Bot stuck!")
            break # Exit loop if bot gives up

        x, y = move_coord

        # --- Data Collection (Before Move) ---
        states_history.append([row[:] for row in game.values]) # Uses game.values
        current_probs = bayes_bot.probabilities.copy() # Copy numpy array
        probabilities_history.append(current_probs)
        # Approx avg prob of UNKNOWN cells leading to this move
        unknown_mask = np.zeros_like(current_probs, dtype=bool)
        for r_idx in range(H):
             for c_idx in range(W):
                  # Check bot's remaining_cells status
                  if bayes_bot.remaining_cells[r_idx, c_idx]:
                       unknown_mask[r_idx, c_idx] = True

        if np.any(unknown_mask): # Avoid division by zero if no unknowns
             total_probability_sum += np.mean(current_probs[unknown_mask])


        if verbose:
            prob_at_move = bayes_bot.probabilities[x, y]
            print(f"\n--- Move {number_of_moves}: BayesBot reveals ({x}, {y}) ---")
            print(f"   (Probability: {prob_at_move:.4f})")

        # --- Execute Move ---
        result = game.make_move(x, y) # Handles reveal and updates game state

        if result["mine_triggered"]:
            mine_triggered = True
            if verbose: print("**** MINE TRIGGERED ****")
            break # Exit loop

        if verbose: print(game)

        if game.win_condition_met:
             if verbose: print("**** WIN CONDITION MET ****")
             break

    # --- End Game ---
    if verbose:
        game.reveal_all()
        print("\n--- Game Summary ---")
        print(game)
        print(f"Total Moves: {number_of_moves}")
        if game.win_condition_met and not mine_triggered:
            print("Game WON!")
        elif mine_triggered:
            print("Game LOST - Mine triggered!")
        else:
             print("Game LOST or Terminated (no win).")

    avg_prob = total_probability_sum / max(1, number_of_moves)
    won = game.win_condition_met and not mine_triggered

    return {
        "moves": number_of_moves,
        "won": won,
        "avg_probability": avg_prob, # Specific to BayesBot
        "mine_triggered": mine_triggered,
        "states": states_history, # Specific to BayesBot
        "probabilities": probabilities_history # Specific to BayesBot
    }



def play_randomly():
    """Plays one game randomly as a baseline comparison."""
    H, W, num_mines = choose_difficulty()
    try:
        game = MineSweeper(H, W, num_mines)
        game.start()
    except ValueError as e:
        print(f"Error initializing game: {e}")
        return # Exit if game can't start

    print("\n--- Starting Random Game ---")
    print(game)

    moves = 0
    mine_triggered = False
    while not game.game_over:
        # Get list of unrevealed and unflagged cells
        unrevealed = []
        for r in range(game.H):
            for c in range(game.W):
                if not game.revealed[r][c] and not game.flagged[r][c]:
                    unrevealed.append((r, c))

        if not unrevealed:
            if not game.check_win_condition(): print("Stuck!")
            break # No more moves possible

        # Pick random cell
        x, y = random.choice(unrevealed)
        print(f"\n--- Move {moves+1}: Random reveal at ({x}, {y}) ---")

        # Execute move
        result = game.make_move(x, y)
        moves += 1
        print(game)

        if result["mine_triggered"]:
            print("**** MINE TRIGGERED ****")
            mine_triggered = True
            # game.game_over is set by make_move/reveal
            break # End game on mine

        if game.win_condition_met:
            print("**** WIN CONDITION MET ****")
            # game.game_over is set by make_move/reveal
            break # End game on win

        # Safety break
        if moves > H * W * 2:
            print("DEADLOCK or excessive moves reached!")
            break

    # --- End Game ---
    game.reveal_all()
    print("\n--- Random Game Summary ---")
    print("Final Board:")
    print(game)

    if game.win_condition_met and not mine_triggered:
        print(f"Game won in {moves} moves!")
    else:
        print(f"Game lost after {moves} moves.")


# --- Main Execution ---
if __name__ == "__main__":
    print("Welcome to Minesweeper Bot Tester!")
    while True:
        print("\nSelect Mode:")
        print("1. Simulate games with BayesBot")
        print("2. Simulate games with SimpleLogicBot") # New option
        print("3. Play one game randomly (Baseline)")
        print("4. Exit")
        mode_choice = input("Enter choice (1-4): ")

        if mode_choice == '1':
            run_simulations(BayesBot) # Pass the class
        elif mode_choice == '2':
            run_simulations(SimpleLogicBot) # Pass the class
        elif mode_choice == '3':
            play_randomly()
        elif mode_choice == '4':
            print("Exiting.")
            break
        else:
            print("Invalid choice.")

