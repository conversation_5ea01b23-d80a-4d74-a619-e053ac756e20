"""
Utility functions for model training and saving with comprehensive naming conventions.
Provides standardized model filename generation and training summary creation.
"""

import os
import time
import tensorflow as tf


def create_model_filename(
    model_arch: str,
    difficulty: str,
    board_height: int,
    board_width: int,
    bot_type: str,
    samples_count: int,
    epochs: int,
    batch_size: int,
    learning_rate: float,
    timestamp: str = None,
    is_temp: bool = False
) -> str:
    """
    Create a comprehensive model filename with all relevant training information.
    
    Args:
        model_arch: Model architecture name (e.g., "SimpleCNN", "ResNet", "Transformer")
        difficulty: Training difficulty (e.g., "Easy", "Intermediate", "Expert", "VariableMines", "VariableSize")
        board_height: Board height dimension
        board_width: Board width dimension  
        bot_type: Training data source (e.g., "SimpleLogic", "Bayes", "Random")
        samples_count: Number of training samples
        epochs: Number of training epochs
        batch_size: Training batch size
        learning_rate: Learning rate used
        timestamp: Timestamp string (auto-generated if None)
        is_temp: Whether this is a temporary filename (before performance metrics)
        
    Returns:
        Comprehensive filename string
        
    Example:
        "SimpleCNN_Easy_9x9_SimpleLogic_8000k_30ep_512bs_LR1e-03_20250624_123456_temp.keras"
        "SimpleCNN_Easy_9x9_SimpleLogic_8000k_30ep_512bs_0.847acc_0.234loss_LR1e-03_20250624_123456.keras"
    """
    if timestamp is None:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # Convert samples to thousands for readability
    samples_k = samples_count // 1000 if samples_count >= 1000 else samples_count
    samples_str = f"{samples_k}k" if samples_count >= 1000 else str(samples_count)
    
    # Format learning rate in scientific notation
    lr_str = f"LR{learning_rate:.0e}"
    
    # Create base filename
    base_filename = (f'{model_arch}_{difficulty}_{board_height}x{board_width}_{bot_type}_{samples_str}_'
                    f'{epochs}ep_{batch_size}bs_{lr_str}_{timestamp}')
    
    if is_temp:
        return f"{base_filename}_temp.keras"
    else:
        return f"{base_filename}.keras"


def create_final_model_filename(
    base_filename: str,
    val_accuracy: float,
    val_loss: float,
    epochs_completed: int = None
) -> str:
    """
    Create final model filename with performance metrics.
    
    Args:
        base_filename: Base filename without performance metrics
        val_accuracy: Best validation accuracy achieved
        val_loss: Best validation loss achieved
        epochs_completed: Actual epochs completed (if different from planned)
        
    Returns:
        Final filename with performance metrics
    """
    # Remove _temp.keras if present
    base = base_filename.replace('_temp.keras', '').replace('.keras', '')
    
    # Update epochs if provided
    if epochs_completed is not None:
        # Replace the epochs part in the filename
        import re
        base = re.sub(r'_\d+ep_', f'_{epochs_completed}ep_', base)
    
    # Add performance metrics
    final_filename = f"{base}_{val_accuracy:.3f}acc_{val_loss:.3f}loss.keras"
    
    return final_filename


def save_training_summary(
    model,
    history,
    model_filename: str,
    models_dir: str,
    training_config: dict,
    performance_metrics: dict,
    training_duration: float
) -> str:
    """
    Save comprehensive training summary to text file.
    
    Args:
        model: Trained Keras model
        history: Training history object
        model_filename: Final model filename
        models_dir: Directory where models are saved
        training_config: Dictionary with training configuration
        performance_metrics: Dictionary with performance metrics
        training_duration: Training duration in seconds
        
    Returns:
        Path to saved summary file
    """
    summary_filename = model_filename.replace('.keras', '_summary.txt')
    summary_path = os.path.join(models_dir, summary_filename)
    
    try:
        with open(summary_path, 'w') as f:
            f.write("="*60 + "\n")
            f.write("MINESWEEPER MODEL TRAINING SUMMARY\n")
            f.write("="*60 + "\n\n")
            
            # Model Information
            f.write("MODEL INFORMATION:\n")
            f.write(f"  • Model File: {model_filename}\n")
            f.write(f"  • Architecture: {training_config.get('model_arch', 'Unknown')}\n")
            f.write(f"  • Difficulty: {training_config.get('difficulty', 'Unknown')}\n")
            f.write(f"  • Board Size: {training_config.get('board_size', 'Unknown')}\n")
            f.write(f"  • Input Shape: {training_config.get('input_shape', 'Unknown')}\n")
            f.write(f"  • Total Parameters: {model.count_params():,}\n\n")
            
            # Training Data
            f.write("TRAINING DATA:\n")
            f.write(f"  • Data Source: {training_config.get('data_source', 'Unknown')}\n")
            f.write(f"  • Bot Type: {training_config.get('bot_type', 'Unknown')}\n")
            f.write(f"  • Total Samples: {training_config.get('total_samples', 0):,}\n")
            f.write(f"  • Training Samples: {training_config.get('train_samples', 0):,}\n")
            f.write(f"  • Validation Samples: {training_config.get('val_samples', 0):,}\n")
            f.write(f"  • Validation Split: {training_config.get('val_split', 0):.1%}\n\n")
            
            # Hyperparameters
            f.write("HYPERPARAMETERS:\n")
            f.write(f"  • Epochs: {training_config.get('epochs_completed', 0)}/{training_config.get('max_epochs', 0)}\n")
            f.write(f"  • Batch Size: {training_config.get('batch_size', 0)}\n")
            f.write(f"  • Learning Rate: {training_config.get('learning_rate', 0)}\n")
            f.write(f"  • Optimizer: {training_config.get('optimizer', 'Unknown')}\n")
            f.write(f"  • Loss Function: {training_config.get('loss_function', 'Unknown')}\n")
            f.write(f"  • Mixed Precision: {training_config.get('mixed_precision', 'Unknown')}\n\n")
            
            # Performance Metrics
            f.write("PERFORMANCE METRICS:\n")
            f.write(f"  • Best Validation Accuracy: {performance_metrics.get('best_val_acc', 0):.4f}\n")
            f.write(f"  • Best Validation Loss: {performance_metrics.get('best_val_loss', 0):.4f}\n")
            f.write(f"  • Final Training Accuracy: {performance_metrics.get('final_train_acc', 0):.4f}\n")
            f.write(f"  • Final Training Loss: {performance_metrics.get('final_train_loss', 0):.4f}\n")
            f.write(f"  • Training Duration: {training_duration:.1f}s ({training_duration/60:.1f} min)\n\n")
            
            # Training History (if available)
            if history and hasattr(history, 'history'):
                f.write("TRAINING HISTORY:\n")
                epochs_completed = len(history.history.get('loss', []))
                for epoch in range(epochs_completed):
                    f.write(f"  Epoch {epoch+1:2d}: "
                           f"loss={history.history['loss'][epoch]:.4f}, "
                           f"acc={history.history.get('move_accuracy', [0])[epoch]:.4f}, "
                           f"val_loss={history.history.get('val_loss', [0])[epoch]:.4f}, "
                           f"val_acc={history.history.get('val_move_accuracy', [0])[epoch]:.4f}\n")
            
            f.write(f"\nTraining completed on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        return summary_path
        
    except Exception as e:
        print(f"⚠️ Error saving training summary: {e}")
        return None


def extract_bot_type_from_filename(hdf5_filename: str) -> str:
    """
    Extract bot type from HDF5 filename.
    
    Args:
        hdf5_filename: HDF5 data filename
        
    Returns:
        Bot type string (e.g., "SimpleLogic", "Bayes")
    """
    if "SimpleLogicBotWrapper" in hdf5_filename or "SimpleLogic" in hdf5_filename:
        return "SimpleLogic"
    elif "BayesBot" in hdf5_filename or "Bayes" in hdf5_filename:
        return "Bayes"
    elif "Random" in hdf5_filename:
        return "Random"
    else:
        return "Unknown"


def extract_difficulty_from_filename(hdf5_filename: str) -> str:
    """
    Extract difficulty from HDF5 filename.
    
    Args:
        hdf5_filename: HDF5 data filename
        
    Returns:
        Difficulty string (e.g., "Easy", "Intermediate", "Expert")
    """
    filename_lower = hdf5_filename.lower()
    
    if "easy" in filename_lower:
        return "Easy"
    elif "intermediate" in filename_lower or "inter" in filename_lower:
        return "Intermediate"
    elif "expert" in filename_lower:
        return "Expert"
    elif "variable_mines" in filename_lower or "variablemines" in filename_lower:
        return "VariableMines"
    elif "variable_size" in filename_lower or "variablesize" in filename_lower:
        return "VariableSize"
    else:
        return "Unknown"


def print_model_info(model_filename: str, val_accuracy: float, models_dir: str):
    """
    Print formatted model information after training completion.
    
    Args:
        model_filename: Final model filename
        val_accuracy: Best validation accuracy
        models_dir: Models directory path
    """
    print("\n" + "="*60)
    print("🎯 MODEL TRAINING COMPLETED")
    print("="*60)
    print(f"📁 Model File: {model_filename}")
    print(f"📊 Best Validation Accuracy: {val_accuracy:.3f}")
    print(f"📂 Location: {os.path.join(models_dir, model_filename)}")
    print("="*60)
