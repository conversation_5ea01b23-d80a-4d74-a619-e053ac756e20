# Minesweeper DL Training Models

This directory contains neural network training scripts for the Minesweeper Deep Learning project, implementing solutions for all three main tasks as specified in the project requirements.

## 📋 Project Overview

The project implements neural networks that can play Minesweeper without external reasoning, using only neural network calculations. The models are trained to outperform traditional logic bots across different scenarios.

## 🎯 Task Structure

### Task 1: Traditional Minesweeper Boards
**3 separate models** for fixed board sizes:
- **Easy**: 9×9 boards with 10 mines
- **Intermediate**: 16×16 boards with 40 mines  
- **Expert**: 30×16 boards with 99 mines

### Task 2: Variable Numbers of Mines
**1 unified model** for variable mine densities:
- 30×30 boards with 0-30% mine density
- Single model handles all mine count variations

### Task 3: Variable Size Boards
**1 unified model** for any board size:
- Handles K×K boards for any K > 5
- Single architecture processes different input sizes dynamically

## 📁 File Structure

```
src/models/
├── TM_easy.py              # Task 1: Easy difficulty training
├── TM_intermediate.py      # Task 1: Intermediate difficulty training
├── TM_expert.py           # Task 1: Expert difficulty training
├── TM_variable_mines.py   # Task 2: Variable mine counts training
├── TM_variable_size.py    # Task 3: Variable board sizes training
├── train_launcher.py      # Unified training launcher
├── validate_models.py     # Model validation script
└── README.md             # This file
```

## 🚀 Quick Start

### 1. Validate All Models
```bash
cd src/models
python validate_models.py --gpu_test --verbose
```

### 2. Train a Specific Model
```bash
# Train easy model
python train_launcher.py --model easy

# Train with GPU check
python train_launcher.py --model variable_size --gpu_check

# Dry run to see what would execute
python train_launcher.py --model all --dry_run
```

### 3. Train All Models Sequentially
```bash
python train_launcher.py --model all
```

## 🔧 Model Architectures

### Traditional Models (Task 1)
- **Easy**: Simple CNN with residual connections
- **Intermediate**: Enhanced CNN with dual residual blocks
- **Expert**: Advanced CNN with triple residual blocks and increased capacity

### Variable Mines Model (Task 2)
- Multi-scale feature extraction (3×3, 5×5, 7×7 kernels)
- Attention mechanism for mine density awareness
- Enhanced preprocessing for variable mine densities

### Variable Size Model (Task 3)
- **Unified architecture** handling any board size K×K (K>5)
- Multi-scale feature extraction for different pattern scales
- Spatial and channel attention mechanisms
- Dynamic padding for variable input sizes
- Size-aware loss function with proper masking

## 📊 Training Features

### GPU Optimization
- Mixed precision training (float16) for RTX 3080 Ti
- Explicit GPU device placement
- Memory growth configuration
- GPU computation verification

### Data Pipeline
- Memory-efficient chunked HDF5 loading
- tf.data optimization with prefetching
- Data augmentation (rotation, flipping)
- Proper preprocessing and normalization

### Training Configuration
- Early stopping with patience
- Model checkpointing (best validation accuracy)
- Learning rate scheduling with exponential decay
- TensorBoard logging for monitoring

## 📈 Monitoring Training

### TensorBoard
```bash
# Monitor training progress
tensorboard --logdir logs/

# Specific model logs
tensorboard --logdir logs/simple_training        # Easy model
tensorboard --logdir logs/intermediate_training  # Intermediate model
tensorboard --logdir logs/expert_training       # Expert model
tensorboard --logdir logs/variable_mines_training # Variable mines
tensorboard --logdir logs/variable_size_training  # Variable sizes
```

### GPU Monitoring
- Monitor GPU utilization in Task Manager (Windows)
- Expected GPU utilization: 70-95% during training
- Training will exit if no GPU is detected

## 🗂️ Data Requirements

### Required HDF5 Files
The training scripts expect the following data files in `data/simulation/`:

**Task 1 - Traditional Boards:**
- `minesweeper_sim_SimpleLogicBotWrapper_easy_H9_W9_M10_N1000_20250623_103834.h5`
- `minesweeper_sim_SimpleLogicBotWrapper_intermediate_H16_W16_M40_N1000_20250623_103908.h5`
- `minesweeper_sim_SimpleLogicBotWrapper_expert_H30_W16_M99_N1000_20250623_104314.h5`

**Task 2 - Variable Mines:**
- `minesweeper_sim_SimpleLogicBotWrapper_variable_mines_30x30_H30_W30_VarMines0.05-0.30_N500_20250623_104724.h5`

**Task 3 - Variable Sizes:**
- `minesweeper_sim_SimpleLogicBotWrapper_variable_size_5-15_VarSizeUpTo50_Density0.20_N1000_20250623_105716.h5`
- `minesweeper_sim_SimpleLogicBotWrapper_variable_size_16-30_VarSizeUpTo50_Density0.20_N1000_20250623_110911.h5`
- `minesweeper_sim_SimpleLogicBotWrapper_variable_size_31-50_VarSizeUpTo50_Density0.20_N1000_20250623_115714.h5`

## ⚙️ Configuration

### Hyperparameters
Each model has optimized hyperparameters:

| Model | Batch Size | Epochs | Learning Rate | Memory Usage |
|-------|------------|--------|---------------|--------------|
| Easy | 512 | 30 | 1e-3 | Low |
| Intermediate | 256 | 30 | 1e-3 | Medium |
| Expert | 128 | 30 | 1e-3 | High |
| Variable Mines | 64 | 40 | 1e-3 | High |
| Variable Size | 64 | 50 | 1e-3 | Very High |

### Directory Structure Created
```
models/
├── trained_simple/          # Easy models
├── trained_intermediate/     # Intermediate models
├── trained_expert/          # Expert models
├── trained_variable_mines/  # Variable mine models
└── trained_variable_size/   # Variable size models

logs/
├── simple_training/
├── intermediate_training/
├── expert_training/
├── variable_mines_training/
└── variable_size_training/
```

## 🧪 Validation

### Pre-training Validation
```bash
# Quick validation
python validate_models.py --quick

# Full validation with GPU test
python validate_models.py --gpu_test --verbose

# Validate specific model
python validate_models.py --model variable_size --verbose
```

### Validation Checks
- ✅ Data file availability and integrity
- ✅ Script syntax and import validation
- ✅ Package dependency verification
- ✅ GPU compatibility testing
- ✅ Model architecture initialization

## 🎯 Expected Training Times

| Model | Estimated Time | GPU Utilization |
|-------|----------------|-----------------|
| Easy | 15-30 minutes | 70-85% |
| Intermediate | 20-40 minutes | 75-90% |
| Expert | 30-60 minutes | 80-95% |
| Variable Mines | 45-90 minutes | 85-95% |
| Variable Size | 60-120 minutes | 85-95% |

## 🔍 Troubleshooting

### Common Issues

**GPU Not Detected:**
```bash
# Check GPU availability
python -c "import tensorflow as tf; print(tf.config.list_physical_devices('GPU'))"
```

**Data Files Missing:**
```bash
# Validate data files
python validate_models.py --model all
```

**Memory Issues:**
- Reduce batch size in the training script
- Close other GPU applications
- Use smaller chunk sizes for data loading

**Import Errors:**
```bash
# Install required packages
pip install tensorflow numpy h5py
```

## 📝 Notes

- All models use the same input representation and output format
- Models implement no external reasoning - only neural network calculations
- Training data generated using SimpleLogicBotWrapper for optimal moves
- Models designed to outperform traditional logic bots
- Comprehensive GPU optimization for RTX 3080 Ti

## 🎉 Success Criteria

Models are considered successful when they:
1. ✅ Load and preprocess data correctly
2. ✅ Initialize model architecture without errors
3. ✅ Train with stable loss convergence
4. ✅ Achieve validation accuracy > 25% (better than random)
5. ✅ Save trained models successfully
6. ✅ Generate reasonable predictions on test data

Ready for training! 🚀
