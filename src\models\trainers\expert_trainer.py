#!/usr/bin/env python3
"""
Expert Trainer: Trainer for expert difficulty (30x16, 99 mines)
Implements task-specific model architecture and training logic.
"""

import tensorflow as tf
from typing import List
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.models.base_trainer import BaseTrainer


class ExpertTrainer(BaseTrainer):
    """Trainer for expert difficulty (30x16, 99 mines)"""
    
    def create_model(self) -> tf.keras.Model:
        """Create CNN model optimized for expert difficulty"""
        H, W = self.config.board_height, self.config.board_width
        
        print(f"🏗️  Creating {self.config.model_arch} model for {H}x{W} board")
        
        model = tf.keras.Sequential([
            # Input layer - 12 channels for board state representation
            tf.keras.layers.Conv2D(
                64, (3, 3), 
                activation='relu', 
                input_shape=(H, W, 12),
                padding='same',
                name='conv2d_1'
            ),
            tf.keras.layers.BatchNormalization(name='batch_norm_1'),
            
            # Second convolutional layer
            tf.keras.layers.Conv2D(
                128, (3, 3), 
                activation='relu',
                padding='same',
                name='conv2d_2'
            ),
            tf.keras.layers.BatchNormalization(name='batch_norm_2'),
            
            # Third convolutional layer
            tf.keras.layers.Conv2D(
                256, (3, 3), 
                activation='relu',
                padding='same',
                name='conv2d_3'
            ),
            tf.keras.layers.BatchNormalization(name='batch_norm_3'),
            
            # Fourth convolutional layer
            tf.keras.layers.Conv2D(
                512, (3, 3), 
                activation='relu',
                padding='same',
                name='conv2d_4'
            ),
            tf.keras.layers.BatchNormalization(name='batch_norm_4'),
            
            # Pooling and dropout
            tf.keras.layers.MaxPooling2D((2, 2), name='max_pool_1'),
            tf.keras.layers.Dropout(0.3, name='dropout_1'),
            
            # Fifth convolutional layer
            tf.keras.layers.Conv2D(
                1024, (3, 3), 
                activation='relu',
                padding='same',
                name='conv2d_5'
            ),
            tf.keras.layers.BatchNormalization(name='batch_norm_5'),
            tf.keras.layers.Dropout(0.3, name='dropout_2'),
            
            # Global average pooling to reduce parameters for large board
            tf.keras.layers.GlobalAveragePooling2D(name='global_avg_pool'),
            
            # Dense layers
            tf.keras.layers.Dense(1024, activation='relu', name='dense_1'),
            tf.keras.layers.Dropout(0.5, name='dropout_3'),
            tf.keras.layers.Dense(512, activation='relu', name='dense_2'),
            tf.keras.layers.Dropout(0.4, name='dropout_4'),
            tf.keras.layers.Dense(256, activation='relu', name='dense_3'),
            tf.keras.layers.Dropout(0.3, name='dropout_5'),
            
            # Output layer - one neuron per board cell
            tf.keras.layers.Dense(H * W, activation='softmax', name='output')
        ], name=f'{self.config.model_arch}_Expert_{H}x{W}')
        
        # Print model summary
        print(f"📋 Model Summary:")
        model.summary()
        
        return model
    
    def get_loss_function(self):
        """Get loss function optimized for expert difficulty"""
        return tf.keras.losses.CategoricalCrossentropy(
            from_logits=False,
            label_smoothing=0.15,  # More label smoothing for expert difficulty
            name='categorical_crossentropy'
        )
    
    def get_metrics(self) -> List:
        """Get metrics for expert difficulty evaluation"""
        return [
            'move_accuracy',
            tf.keras.metrics.TopKCategoricalAccuracy(k=3, name='top_3_accuracy'),
            tf.keras.metrics.TopKCategoricalAccuracy(k=5, name='top_5_accuracy'),
            tf.keras.metrics.TopKCategoricalAccuracy(k=10, name='top_10_accuracy'),
            tf.keras.metrics.TopKCategoricalAccuracy(k=20, name='top_20_accuracy'),
            tf.keras.metrics.CategoricalCrossentropy(name='categorical_crossentropy_metric')
        ]
    
    def _get_sample_count(self) -> int:
        """Get sample count from data file for expert difficulty"""
        # Try to get actual count from data file
        if self.config.data_file:
            try:
                import h5py
                with h5py.File(self.config.data_file, 'r') as f:
                    if 'moves' in f:
                        return len(f['moves'])
                    elif 'X' in f:
                        return len(f['X'])
            except Exception:
                pass
        
        # Default estimate for expert difficulty
        return 100000  # Typical for expert difficulty training data
